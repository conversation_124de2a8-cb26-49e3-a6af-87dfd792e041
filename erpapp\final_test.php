<?php
/**
 * اختبار نهائي شامل للنظام
 */

// تضمين ملفات النظام
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/App/Core/FieldManager.php';
require_once __DIR__ . '/App/Modules/Inventory/Inventory/Models/Product.php';
require_once __DIR__ . '/App/Helpers/functions.php';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h1>🔧 الاختبار النهائي الشامل</h1>";
    echo "<hr>";

    // معرف الشركة والمستخدم
    $companyId = 4;
    $userId = 32;

    echo "<h3>1️⃣ فحص بنية جدول inventory_products:</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE inventory_products");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasProductCode = false;
        foreach ($columns as $column) {
            if ($column['Field'] == 'product_code') {
                $hasProductCode = true;
            }
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (!$hasProductCode) {
            echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>❌ مشكلة في بنية الجدول</h4>";
            echo "<p>عمود product_code غير موجود في الجدول!</p>";
            echo "<p>يجب تشغيل fix_table_structure.sql</p>";
            echo "</div>";
            exit;
        } else {
            echo "<p style='color: green;'>✅ بنية الجدول صحيحة</p>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ خطأ في فحص الجدول</h4>";
        echo "<p>الخطأ: " . $e->getMessage() . "</p>";
        echo "<p>يجب إنشاء الجدول أولاً باستخدام fix_table_structure.sql</p>";
        echo "</div>";
        exit;
    }

    echo "<h3>2️⃣ فحص النظام الديناميكي:</h3>";
    
    // إنشاء النماذج
    $fieldManager = new App\Core\FieldManager($pdo);
    $productModel = new App\Modules\Inventory\Inventory\Models\Product($pdo, $fieldManager);

    // فحص إعدادات الشركة
    $hasSettings = $fieldManager->hasCompanyFieldSettings($companyId, 'inventory', 'products');
    echo "<p><strong>إعدادات الشركة:</strong> " . ($hasSettings ? "✅ موجودة" : "❌ غير موجودة") . "</p>";

    if (!$hasSettings) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ إعدادات النظام الديناميكي غير موجودة</h4>";
        echo "<p>يجب تشغيل dynamic_system_tables.sql أولاً</p>";
        echo "</div>";
    }

    // فحص التبويبات
    $tabs = $fieldManager->getCompanyFieldsGroupedByTabs($companyId, 'inventory', 'products', 'form');
    echo "<p><strong>عدد التبويبات:</strong> " . count($tabs) . "</p>";

    echo "<h3>3️⃣ اختبار إنشاء منتج:</h3>";

    // بيانات المنتج التجريبي
    $testData = [
        'product_code' => 'FINAL_TEST_' . time(),
        'product_name_ar' => 'منتج الاختبار النهائي',
        'product_name_en' => 'Final Test Product',
        'category_id' => 1,
        'unit_id' => 1,
        'cost_price' => 100.00,
        'selling_price' => 150.00,
        'description_ar' => 'منتج تم إنشاؤه في الاختبار النهائي',
        'barcode' => 'FINAL' . time(),
        'is_active' => 1
    ];

    echo "<p><strong>بيانات الاختبار:</strong></p>";
    echo "<ul>";
    foreach ($testData as $key => $value) {
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";

    try {
        echo "<p>🚀 محاولة إنشاء المنتج...</p>";
        
        $productId = $productModel->createDynamicProduct($companyId, $testData, $userId);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🎉 نجح إنشاء المنتج!</h4>";
        echo "<p><strong>معرف المنتج الجديد:</strong> $productId</p>";
        echo "</div>";

        // التحقق من البيانات المحفوظة
        echo "<h3>4️⃣ التحقق من البيانات المحفوظة:</h3>";
        
        $stmt = $pdo->prepare("SELECT * FROM inventory_products WHERE product_id = ?");
        $stmt->execute([$productId]);
        $savedProduct = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($savedProduct) {
            echo "<p>✅ <strong>تم حفظ البيانات في الجدول الأساسي:</strong></p>";
            echo "<ul>";
            echo "<li>كود المنتج: {$savedProduct['product_code']}</li>";
            echo "<li>اسم المنتج: {$savedProduct['product_name_ar']}</li>";
            echo "<li>سعر التكلفة: {$savedProduct['cost_price']}</li>";
            echo "<li>سعر البيع: {$savedProduct['selling_price']}</li>";
            echo "</ul>";
        }

        // فحص القيم الديناميكية
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM dynamic_field_values 
            WHERE company_id = ? AND record_id = ?
        ");
        $stmt->execute([$companyId, $productId]);
        $dynamicCount = $stmt->fetchColumn();
        
        echo "<p>✅ <strong>القيم الديناميكية:</strong> $dynamicCount قيمة محفوظة</p>";

        echo "<h3>5️⃣ النتيجة النهائية:</h3>";
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; text-align: center;'>";
        echo "<h2 style='color: #155724; margin: 0;'>🎉 النظام يعمل بشكل مثالي!</h2>";
        echo "<p style='margin: 10px 0 0 0;'>يمكنك الآن استخدام نموذج إضافة المنتجات بثقة تامة.</p>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ فشل في إنشاء المنتج!</h4>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
        
        // اقتراحات الحل
        echo "<h5>💡 اقتراحات الحل:</h5>";
        echo "<ol>";
        echo "<li>تأكد من تشغيل fix_table_structure.sql</li>";
        echo "<li>تأكد من وجود فئات ووحدات قياس بمعرفات 1 و 2</li>";
        echo "<li>تأكد من تشغيل dynamic_system_tables.sql</li>";
        echo "<li>تحقق من صلاحيات قاعدة البيانات</li>";
        echo "</ol>";
        echo "</div>";
    }

    echo "<h3>🔗 روابط مفيدة:</h3>";
    echo "<ul>";
    echo "<li><a href='/erpapp/inventory/products/create' target='_blank'>نموذج إضافة منتج</a></li>";
    echo "<li><a href='/erpapp/inventory/products' target='_blank'>قائمة المنتجات</a></li>";
    echo "<li><a href='/erpapp/test_product_creation.php' target='_blank'>اختبار إنشاء منتج</a></li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<h2>❌ خطأ في قاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>تحقق من إعدادات الاتصال بقاعدة البيانات</p>";
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
th { background-color: #f0f0f0; }
h1, h2, h3 { color: #333; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
