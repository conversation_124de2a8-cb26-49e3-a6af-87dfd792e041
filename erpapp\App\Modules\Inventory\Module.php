<?php
namespace App\Modules\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات لوحة تحكم المخزون
        add_route('GET', '/inventory', 'App\Modules\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/inventory/dashboard', 'App\Modules\Inventory\Controllers\InventoryController@dashboard');
        
        // مسارات المنتجات (النظام الديناميكي)
        add_route('GET', '/inventory/products', 'App\Modules\Inventory\Controllers\ProductController@index');
        add_route('GET', '/inventory/products/create', 'App\Modules\Inventory\Controllers\ProductController@create');
        add_route('POST', '/inventory/products/store', 'App\Modules\Inventory\Controllers\ProductController@store');
        add_route('POST', '/inventory/products/auto-save', 'App\Modules\Inventory\Controllers\ProductController@autoSave');
        add_route('GET', '/inventory/products/{id}', 'App\Modules\Inventory\Controllers\ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', 'App\Modules\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', 'App\Modules\Inventory\Controllers\ProductController@update');
        add_route('POST', '/inventory/products/{id}/delete', 'App\Modules\Inventory\Controllers\ProductController@delete');

        // مسارات إعدادات الحقول الديناميكية
        add_route('GET', '/inventory/settings/fields', 'App\Modules\Inventory\Controllers\FieldSettingsController@index');
        add_route('POST', '/inventory/settings/fields/save', 'App\Modules\Inventory\Controllers\FieldSettingsController@save');
        add_route('POST', '/inventory/settings/fields/reset', 'App\Modules\Inventory\Controllers\FieldSettingsController@reset');
        add_route('GET', '/inventory/settings/fields/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@preview');
        
        // مسارات الفئات
        add_route('GET', '/inventory/categories', 'App\Modules\Inventory\Controllers\CategoryController@index');
        add_route('GET', '/inventory/categories/create', 'App\Modules\Inventory\Controllers\CategoryController@create');
        add_route('POST', '/inventory/categories/store', 'App\Modules\Inventory\Controllers\CategoryController@store');
        add_route('GET', '/inventory/categories/{id}', 'App\Modules\Inventory\Controllers\CategoryController@show');
        add_route('GET', '/inventory/categories/{id}/edit', 'App\Modules\Inventory\Controllers\CategoryController@edit');
        add_route('POST', '/inventory/categories/{id}/update', 'App\Modules\Inventory\Controllers\CategoryController@update');
        add_route('POST', '/inventory/categories/{id}/delete', 'App\Modules\Inventory\Controllers\CategoryController@delete');
        
        // مسارات المخازن
        add_route('GET', '/inventory/warehouses', 'App\Modules\Inventory\Controllers\WarehouseController@index');
        add_route('GET', '/inventory/warehouses/create', 'App\Modules\Inventory\Controllers\WarehouseController@create');
        add_route('POST', '/inventory/warehouses/store', 'App\Modules\Inventory\Controllers\WarehouseController@store');
        add_route('GET', '/inventory/warehouses/{id}', 'App\Modules\Inventory\Controllers\WarehouseController@show');
        add_route('GET', '/inventory/warehouses/{id}/edit', 'App\Modules\Inventory\Controllers\WarehouseController@edit');
        add_route('POST', '/inventory/warehouses/{id}/update', 'App\Modules\Inventory\Controllers\WarehouseController@update');
        add_route('POST', '/inventory/warehouses/{id}/delete', 'App\Modules\Inventory\Controllers\WarehouseController@delete');
        
        // مسارات أرصدة المخزون
        add_route('GET', '/inventory/stock', 'App\Modules\Inventory\Controllers\StockController@index');
        add_route('GET', '/inventory/stock/product/{id}', 'App\Modules\Inventory\Controllers\StockController@productStock');
        add_route('GET', '/inventory/stock/warehouse/{id}', 'App\Modules\Inventory\Controllers\StockController@warehouseStock');
        add_route('GET', '/inventory/stock/low-stock', 'App\Modules\Inventory\Controllers\StockController@lowStock');
        add_route('GET', '/inventory/stock/out-of-stock', 'App\Modules\Inventory\Controllers\StockController@outOfStock');
        
        // مسارات حركات المخزون
        add_route('GET', '/inventory/movements', 'App\Modules\Inventory\Controllers\MovementController@index');
        add_route('GET', '/inventory/movements/create', 'App\Modules\Inventory\Controllers\MovementController@create');
        add_route('POST', '/inventory/movements/store', 'App\Modules\Inventory\Controllers\MovementController@store');
        add_route('GET', '/inventory/movements/{id}', 'App\Modules\Inventory\Controllers\MovementController@show');
        
        // مسارات التحويلات
        add_route('GET', '/inventory/transfers', 'App\Modules\Inventory\Controllers\TransferController@index');
        add_route('GET', '/inventory/transfers/create', 'App\Modules\Inventory\Controllers\TransferController@create');
        add_route('POST', '/inventory/transfers/store', 'App\Modules\Inventory\Controllers\TransferController@store');
        add_route('GET', '/inventory/transfers/{id}', 'App\Modules\Inventory\Controllers\TransferController@show');
        add_route('POST', '/inventory/transfers/{id}/approve', 'App\Modules\Inventory\Controllers\TransferController@approve');
        add_route('POST', '/inventory/transfers/{id}/complete', 'App\Modules\Inventory\Controllers\TransferController@complete');
        
        // مسارات وحدات القياس
        add_route('GET', '/inventory/units', 'App\Modules\Inventory\Controllers\UnitController@index');
        add_route('GET', '/inventory/units/create', 'App\Modules\Inventory\Controllers\UnitController@create');
        add_route('POST', '/inventory/units/store', 'App\Modules\Inventory\Controllers\UnitController@store');
        add_route('GET', '/inventory/units/{id}/edit', 'App\Modules\Inventory\Controllers\UnitController@edit');
        add_route('POST', '/inventory/units/{id}/update', 'App\Modules\Inventory\Controllers\UnitController@update');
        add_route('POST', '/inventory/units/{id}/delete', 'App\Modules\Inventory\Controllers\UnitController@delete');
        
        // مسارات التقارير
        add_route('GET', '/inventory/reports', 'App\Modules\Inventory\Controllers\ReportController@index');
        add_route('GET', '/inventory/reports/stock-report', 'App\Modules\Inventory\Controllers\ReportController@stockReport');
        add_route('GET', '/inventory/reports/movement-report', 'App\Modules\Inventory\Controllers\ReportController@movementReport');
        add_route('GET', '/inventory/reports/valuation-report', 'App\Modules\Inventory\Controllers\ReportController@valuationReport');
        
        // مسارات الجرد
        add_route('GET', '/inventory/audits', 'App\Modules\Inventory\Controllers\AuditController@index');
        add_route('GET', '/inventory/audits/create', 'App\Modules\Inventory\Controllers\AuditController@create');
        add_route('POST', '/inventory/audits/store', 'App\Modules\Inventory\Controllers\AuditController@store');
        add_route('GET', '/inventory/audits/{id}', 'App\Modules\Inventory\Controllers\AuditController@show');
        add_route('POST', '/inventory/audits/{id}/start', 'App\Modules\Inventory\Controllers\AuditController@start');
        add_route('POST', '/inventory/audits/{id}/complete', 'App\Modules\Inventory\Controllers\AuditController@complete');
    }
}
