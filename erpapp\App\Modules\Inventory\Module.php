<?php
namespace App\Modules\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون - النظام الديناميكي الجديد
 * تم تنظيف المسارات من النظام القديم
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     * النظام الديناميكي الجديد فقط
     */
    public function registerRoutes()
    {
        // ========================================
        // مسارات لوحة تحكم المخزون
        // ========================================
        add_route('GET', '/inventory', 'App\Modules\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/inventory/dashboard', 'App\Modules\Inventory\Controllers\InventoryController@dashboard');

        // ========================================
        // مسارات المنتجات (النظام الديناميكي الجديد)
        // ========================================
        add_route('GET', '/inventory/products', 'App\Modules\Inventory\Controllers\ProductController@index');
        add_route('POST', '/inventory/products/search', 'App\Modules\Inventory\Controllers\ProductController@search');
        add_route('GET', '/inventory/products/create', 'App\Modules\Inventory\Controllers\ProductController@create');
        add_route('POST', '/inventory/products/store', 'App\Modules\Inventory\Controllers\ProductController@store');
        add_route('POST', '/inventory/products/auto-save', 'App\Modules\Inventory\Controllers\ProductController@autoSave');
        add_route('GET', '/inventory/products/{id}', 'App\Modules\Inventory\Controllers\ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', 'App\Modules\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', 'App\Modules\Inventory\Controllers\ProductController@update');
        add_route('POST', '/inventory/products/{id}/delete', 'App\Modules\Inventory\Controllers\ProductController@delete');

        // ========================================
        // مسارات إعدادات الحقول الديناميكية
        // ========================================
        add_route('GET', '/inventory/settings/fields', 'App\Modules\Inventory\Controllers\FieldSettingsController@index');
        add_route('POST', '/inventory/settings/fields/save', 'App\Modules\Inventory\Controllers\FieldSettingsController@save');
        add_route('POST', '/inventory/settings/fields/reset', 'App\Modules\Inventory\Controllers\FieldSettingsController@reset');
        add_route('GET', '/inventory/settings/fields/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@preview');
        add_route('GET', '/inventory/settings/fields/export', 'App\Modules\Inventory\Controllers\FieldSettingsController@export');
        add_route('POST', '/inventory/settings/fields/import', 'App\Modules\Inventory\Controllers\FieldSettingsController@import');

        // ========================================
        // المسارات المستقبلية (سيتم تطويرها لاحقاً)
        // ========================================

        // مسارات الفئات الديناميكية (قريباً)
        // add_route('GET', '/inventory/categories', 'App\Modules\Inventory\Controllers\CategoryController@index');

        // مسارات المخازن الديناميكية (قريباً)
        // add_route('GET', '/inventory/warehouses', 'App\Modules\Inventory\Controllers\WarehouseController@index');

        // مسارات أرصدة المخزون الديناميكية (قريباً)
        // add_route('GET', '/inventory/stock', 'App\Modules\Inventory\Controllers\StockController@index');

        // مسارات حركات المخزون الديناميكية (قريباً)
        // add_route('GET', '/inventory/movements', 'App\Modules\Inventory\Controllers\MovementController@index');

        // مسارات التقارير الديناميكية (قريباً)
        // add_route('GET', '/inventory/reports', 'App\Modules\Inventory\Controllers\ReportController@index');

        // مسارات وحدات القياس الديناميكية (قريباً)
        // add_route('GET', '/inventory/units', 'App\Modules\Inventory\Controllers\UnitController@index');

        // مسارات الجرد الديناميكي (قريباً)
        // add_route('GET', '/inventory/audits', 'App\Modules\Inventory\Controllers\AuditController@index');
    }
}
