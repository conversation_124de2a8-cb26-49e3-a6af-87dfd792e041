<?php
namespace App\Modules\Accounting\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون - قسم المحاسبة
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات لوحة تحكم المخزون
        add_route('GET', '/Accounting/Inventory', 'App\Modules\Accounting\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/Accounting/Inventory/dashboard', 'App\Modules\Accounting\Inventory\Controllers\InventoryController@dashboard');
        
        // مسارات المنتجات
        add_route('GET', '/Accounting/Inventory/products', 'App\Modules\Accounting\Inventory\Controllers\ProductController@index');
        add_route('GET', '/Accounting/Inventory/products/create', 'App\Modules\Accounting\Inventory\Controllers\ProductController@create');
        add_route('POST', '/Accounting/Inventory/products/store', 'App\Modules\Accounting\Inventory\Controllers\ProductController@store');
        add_route('GET', '/Accounting/Inventory/products/{id}', 'App\Modules\Accounting\Inventory\Controllers\ProductController@show');
        add_route('GET', '/Accounting/Inventory/products/{id}/edit', 'App\Modules\Accounting\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/Accounting/Inventory/products/{id}/update', 'App\Modules\Accounting\Inventory\Controllers\ProductController@update');
        add_route('POST', '/Accounting/Inventory/products/{id}/delete', 'App\Modules\Accounting\Inventory\Controllers\ProductController@delete');
        
        // مسارات الفئات
        add_route('GET', '/Accounting/Inventory/categories', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@index');
        add_route('GET', '/Accounting/Inventory/categories/create', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@create');
        add_route('POST', '/Accounting/Inventory/categories/store', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@store');
        add_route('GET', '/Accounting/Inventory/categories/{id}', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@show');
        add_route('GET', '/Accounting/Inventory/categories/{id}/edit', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@edit');
        add_route('POST', '/Accounting/Inventory/categories/{id}/update', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@update');
        add_route('POST', '/Accounting/Inventory/categories/{id}/delete', 'App\Modules\Accounting\Inventory\Controllers\CategoryController@delete');
        
        // مسارات المخازن
        add_route('GET', '/Accounting/Inventory/warehouses', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@index');
        add_route('GET', '/Accounting/Inventory/warehouses/create', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@create');
        add_route('POST', '/Accounting/Inventory/warehouses/store', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@store');
        add_route('GET', '/Accounting/Inventory/warehouses/{id}', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@show');
        add_route('GET', '/Accounting/Inventory/warehouses/{id}/edit', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@edit');
        add_route('POST', '/Accounting/Inventory/warehouses/{id}/update', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@update');
        add_route('POST', '/Accounting/Inventory/warehouses/{id}/delete', 'App\Modules\Accounting\Inventory\Controllers\WarehouseController@delete');
        
        // مسارات أرصدة المخزون
        add_route('GET', '/Accounting/Inventory/stock', 'App\Modules\Accounting\Inventory\Controllers\StockController@index');
        add_route('GET', '/Accounting/Inventory/stock/product/{id}', 'App\Modules\Accounting\Inventory\Controllers\StockController@productStock');
        add_route('GET', '/Accounting/Inventory/stock/warehouse/{id}', 'App\Modules\Accounting\Inventory\Controllers\StockController@warehouseStock');
        add_route('GET', '/Accounting/Inventory/stock/low-stock', 'App\Modules\Accounting\Inventory\Controllers\StockController@lowStock');
        add_route('GET', '/Accounting/Inventory/stock/out-of-stock', 'App\Modules\Accounting\Inventory\Controllers\StockController@outOfStock');
        
        // مسارات حركات المخزون
        add_route('GET', '/Accounting/Inventory/movements', 'App\Modules\Accounting\Inventory\Controllers\MovementController@index');
        add_route('GET', '/Accounting/Inventory/movements/create', 'App\Modules\Accounting\Inventory\Controllers\MovementController@create');
        add_route('POST', '/Accounting/Inventory/movements/store', 'App\Modules\Accounting\Inventory\Controllers\MovementController@store');
        add_route('GET', '/Accounting/Inventory/movements/{id}', 'App\Modules\Accounting\Inventory\Controllers\MovementController@show');
        
        // مسارات التحويلات
        add_route('GET', '/Accounting/Inventory/transfers', 'App\Modules\Accounting\Inventory\Controllers\TransferController@index');
        add_route('GET', '/Accounting/Inventory/transfers/create', 'App\Modules\Accounting\Inventory\Controllers\TransferController@create');
        add_route('POST', '/Accounting/Inventory/transfers/store', 'App\Modules\Accounting\Inventory\Controllers\TransferController@store');
        add_route('GET', '/Accounting/Inventory/transfers/{id}', 'App\Modules\Accounting\Inventory\Controllers\TransferController@show');
        add_route('POST', '/Accounting/Inventory/transfers/{id}/approve', 'App\Modules\Accounting\Inventory\Controllers\TransferController@approve');
        add_route('POST', '/Accounting/Inventory/transfers/{id}/complete', 'App\Modules\Accounting\Inventory\Controllers\TransferController@complete');
        
        // مسارات وحدات القياس
        add_route('GET', '/Accounting/Inventory/units', 'App\Modules\Accounting\Inventory\Controllers\UnitController@index');
        add_route('GET', '/Accounting/Inventory/units/create', 'App\Modules\Accounting\Inventory\Controllers\UnitController@create');
        add_route('POST', '/Accounting/Inventory/units/store', 'App\Modules\Accounting\Inventory\Controllers\UnitController@store');
        add_route('GET', '/Accounting/Inventory/units/{id}/edit', 'App\Modules\Accounting\Inventory\Controllers\UnitController@edit');
        add_route('POST', '/Accounting/Inventory/units/{id}/update', 'App\Modules\Accounting\Inventory\Controllers\UnitController@update');
        add_route('POST', '/Accounting/Inventory/units/{id}/delete', 'App\Modules\Accounting\Inventory\Controllers\UnitController@delete');
        
        // مسارات التقارير
        add_route('GET', '/Accounting/Inventory/reports', 'App\Modules\Accounting\Inventory\Controllers\ReportController@index');
        add_route('GET', '/Accounting/Inventory/reports/stock-report', 'App\Modules\Accounting\Inventory\Controllers\ReportController@stockReport');
        add_route('GET', '/Accounting/Inventory/reports/movement-report', 'App\Modules\Accounting\Inventory\Controllers\ReportController@movementReport');
        add_route('GET', '/Accounting/Inventory/reports/valuation-report', 'App\Modules\Accounting\Inventory\Controllers\ReportController@valuationReport');
        
        // مسارات الجرد
        add_route('GET', '/Accounting/Inventory/audits', 'App\Modules\Accounting\Inventory\Controllers\AuditController@index');
        add_route('GET', '/Accounting/Inventory/audits/create', 'App\Modules\Accounting\Inventory\Controllers\AuditController@create');
        add_route('POST', '/Accounting/Inventory/audits/store', 'App\Modules\Accounting\Inventory\Controllers\AuditController@store');
        add_route('GET', '/Accounting/Inventory/audits/{id}', 'App\Modules\Accounting\Inventory\Controllers\AuditController@show');
        add_route('POST', '/Accounting/Inventory/audits/{id}/start', 'App\Modules\Accounting\Inventory\Controllers\AuditController@start');
        add_route('POST', '/Accounting/Inventory/audits/{id}/complete', 'App\Modules\Accounting\Inventory\Controllers\AuditController@complete');

        // مسارات إعدادات الحقول الديناميكية
        add_route('GET', '/inventory/settings/fields', 'App\Modules\Inventory\Controllers\FieldSettingsController@index');
        add_route('POST', '/inventory/settings/fields/update', 'App\Modules\Inventory\Controllers\FieldSettingsController@updateFields');
        add_route('POST', '/inventory/settings/fields/create-table', 'App\Modules\Inventory\Controllers\FieldSettingsController@createTable');
        add_route('POST', '/inventory/settings/fields/create-defaults', 'App\Modules\Inventory\Controllers\FieldSettingsController@createDefaultSettings');
        add_route('GET', '/inventory/settings/fields/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@previewTable');

        // مسارات المنتجات الديناميكية
        add_route('GET', '/inventory/dynamic/products', 'App\Modules\Inventory\Controllers\DynamicProductController@index');
        add_route('GET', '/inventory/dynamic/products/create', 'App\Modules\Inventory\Controllers\DynamicProductController@create');
        add_route('POST', '/inventory/dynamic/products/store', 'App\Modules\Inventory\Controllers\DynamicProductController@store');
        add_route('GET', '/inventory/dynamic/products/{id}', 'App\Modules\Inventory\Controllers\DynamicProductController@show');
        add_route('GET', '/inventory/dynamic/products/{id}/edit', 'App\Modules\Inventory\Controllers\DynamicProductController@edit');
        add_route('POST', '/inventory/dynamic/products/{id}/update', 'App\Modules\Inventory\Controllers\DynamicProductController@update');
        add_route('POST', '/inventory/dynamic/products/{id}/delete', 'App\Modules\Inventory\Controllers\DynamicProductController@delete');
    }
}
