<?php
namespace App\Modules\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون - النظام الديناميكي الجديد
 * تم تنظيف المسارات من النظام القديم
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     * النظام الديناميكي الجديد فقط
     */
    public function registerRoutes()
    {
        // ========================================
        // مسارات لوحة تحكم المخزون
        // ========================================
        add_route('GET', '/inventory', 'App\Modules\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/inventory/dashboard', 'App\Modules\Inventory\Controllers\InventoryController@dashboard');

        // ========================================
        // مسارات المنتجات (النظام الديناميكي الجديد)
        // ========================================
        add_route('GET', '/inventory/products', 'App\Modules\Inventory\Controllers\ProductController@index');
        add_route('POST', '/inventory/products/search', 'App\Modules\Inventory\Controllers\ProductController@search');
        add_route('GET', '/inventory/products/create', 'App\Modules\Inventory\Controllers\ProductController@create');
        add_route('POST', '/inventory/products/store', 'App\Modules\Inventory\Controllers\ProductController@store');
        add_route('POST', '/inventory/products/auto-save', 'App\Modules\Inventory\Controllers\ProductController@autoSave');
        add_route('GET', '/inventory/products/{id}', 'App\Modules\Inventory\Controllers\ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', 'App\Modules\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', 'App\Modules\Inventory\Controllers\ProductController@update');
        add_route('POST', '/inventory/products/{id}/delete', 'App\Modules\Inventory\Controllers\ProductController@delete');

        // ========================================
        // مسارات إعدادات الحقول الديناميكية - مسارات منفصلة لكل نوع
        // ========================================

        // مسارات إعدادات حقول المنتجات
        add_route('GET', '/inventory/settings/fields_product', 'App\Modules\Inventory\Controllers\FieldSettingsController@productFields');
        add_route('POST', '/inventory/settings/fields_product/save', 'App\Modules\Inventory\Controllers\FieldSettingsController@saveProductFields');
        add_route('POST', '/inventory/settings/fields_product/reset', 'App\Modules\Inventory\Controllers\FieldSettingsController@resetProductFields');
        add_route('GET', '/inventory/settings/fields_product/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@previewProductFields');

        // مسارات إعدادات حقول الفئات
        add_route('GET', '/inventory/settings/fields_category', 'App\Modules\Inventory\Controllers\FieldSettingsController@categoryFields');
        add_route('POST', '/inventory/settings/fields_category/save', 'App\Modules\Inventory\Controllers\FieldSettingsController@saveCategoryFields');
        add_route('POST', '/inventory/settings/fields_category/reset', 'App\Modules\Inventory\Controllers\FieldSettingsController@resetCategoryFields');
        add_route('GET', '/inventory/settings/fields_category/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@previewCategoryFields');

        // المسارات القديمة (للتوافق مع النظام القديم)
        add_route('GET', '/inventory/settings/fields', 'App\Modules\Inventory\Controllers\FieldSettingsController@index');
        add_route('POST', '/inventory/settings/fields/save', 'App\Modules\Inventory\Controllers\FieldSettingsController@save');
        add_route('POST', '/inventory/settings/fields/reset', 'App\Modules\Inventory\Controllers\FieldSettingsController@reset');
        add_route('GET', '/inventory/settings/fields/preview', 'App\Modules\Inventory\Controllers\FieldSettingsController@preview');
        add_route('GET', '/inventory/settings/fields/export', 'App\Modules\Inventory\Controllers\FieldSettingsController@export');
        add_route('POST', '/inventory/settings/fields/import', 'App\Modules\Inventory\Controllers\FieldSettingsController@import');

        // ========================================
        // مسارات API للنظام الديناميكي
        // ========================================

        // API المنتجات
        add_route('GET', '/api/inventory/products', 'App\Modules\Inventory\Controllers\ProductController@apiIndex');
        add_route('GET', '/api/inventory/products/{id}', 'App\Modules\Inventory\Controllers\ProductController@apiShow');
        add_route('POST', '/api/inventory/products/search', 'App\Modules\Inventory\Controllers\ProductController@apiSearch');

        // API الفئات
        add_route('GET', '/api/inventory/categories', 'App\Modules\Inventory\Controllers\CategoryController@apiIndex');
        add_route('GET', '/api/inventory/categories/{id}', 'App\Modules\Inventory\Controllers\CategoryController@apiShow');
        add_route('POST', '/api/inventory/categories/search', 'App\Modules\Inventory\Controllers\CategoryController@apiSearch');
        add_route('GET', '/api/inventory/categories/select', 'App\Modules\Inventory\Controllers\CategoryController@apiSelect');

        // API عام
        add_route('GET', '/api/inventory/fields', 'App\Modules\Inventory\Controllers\FieldSettingsController@apiFields');
        add_route('GET', '/api/inventory/stats', 'App\Modules\Inventory\Controllers\InventoryController@apiStats');

        // ========================================
        // مسارات الفئات (النظام الديناميكي الجديد)
        // ========================================
        add_route('GET', '/inventory/categories', 'App\Modules\Inventory\Controllers\CategoryController@index');
        add_route('POST', '/inventory/categories/search', 'App\Modules\Inventory\Controllers\CategoryController@search');
        add_route('GET', '/inventory/categories/create', 'App\Modules\Inventory\Controllers\CategoryController@create');
        add_route('POST', '/inventory/categories/store', 'App\Modules\Inventory\Controllers\CategoryController@store');
        add_route('POST', '/inventory/categories/auto-save', 'App\Modules\Inventory\Controllers\CategoryController@autoSave');
        add_route('GET', '/inventory/categories/{id}', 'App\Modules\Inventory\Controllers\CategoryController@show');
        add_route('GET', '/inventory/categories/{id}/edit', 'App\Modules\Inventory\Controllers\CategoryController@edit');
        add_route('POST', '/inventory/categories/{id}/update', 'App\Modules\Inventory\Controllers\CategoryController@update');
        add_route('POST', '/inventory/categories/{id}/delete', 'App\Modules\Inventory\Controllers\CategoryController@delete');
        add_route('GET', '/inventory/categories/export', 'App\Modules\Inventory\Controllers\CategoryController@export');
        add_route('POST', '/inventory/categories/import', 'App\Modules\Inventory\Controllers\CategoryController@import');

        // ========================================
        // المسارات المستقبلية (سيتم تطويرها لاحقاً)
        // ========================================

        // مسارات المخازن الديناميكية (قريباً)
        // add_route('GET', '/inventory/warehouses', 'App\Modules\Inventory\Controllers\WarehouseController@index');

        // مسارات أرصدة المخزون الديناميكية (قريباً)
        // add_route('GET', '/inventory/stock', 'App\Modules\Inventory\Controllers\StockController@index');

        // مسارات حركات المخزون الديناميكية (قريباً)
        // add_route('GET', '/inventory/movements', 'App\Modules\Inventory\Controllers\MovementController@index');

        // مسارات التقارير الديناميكية (قريباً)
        // add_route('GET', '/inventory/reports', 'App\Modules\Inventory\Controllers\ReportController@index');

        // مسارات وحدات القياس الديناميكية (قريباً)
        // add_route('GET', '/inventory/units', 'App\Modules\Inventory\Controllers\UnitController@index');

        // مسارات الجرد الديناميكي (قريباً)
        // add_route('GET', '/inventory/audits', 'App\Modules\Inventory\Controllers\AuditController@index');
    }
}
