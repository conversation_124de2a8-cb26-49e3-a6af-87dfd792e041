<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;
use App\Core\FieldManager;

/**
 * Category Model - نموذج الفئات
 * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
 */
class Category
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Field manager for dynamic fields
     */
    protected $fieldManager;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->fieldManager = new FieldManager($db);
    }

    // ========================================
    // الدوال الديناميكية الجديدة
    // ========================================



    /**
     * الحصول على الفئات مع القيم الديناميكية للعرض في الجداول
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicCategories($companyId, $tableFields, $filters = [])
    {
        // الحصول على معرفات الفئات الفريدة للشركة
        $categoryIdsSql = "SELECT DISTINCT record_id
                         FROM dynamic_field_values
                         WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'categories'
                         ORDER BY record_id DESC";

        if (!empty($filters['limit'])) {
            $categoryIdsSql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($categoryIdsSql);
        $stmt->execute([$companyId]);
        $categoryIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($categoryIds)) {
            return [];
        }

        $results = [];

        // جلب بيانات كل فئة
        foreach ($categoryIds as $categoryId) {
            $category = $this->getDynamicCategory($categoryId, $companyId, $tableFields);
            if ($category) {
                // تطبيق الفلاتر
                $includeCategory = true;

                // فلتر البحث
                if (!empty($filters['search'])) {
                    $includeCategory = false;
                    foreach($tableFields as $field) {
                        if ($field['is_searchable'] && isset($category[$field['field_name']])) {
                            if (stripos($category[$field['field_name']], $filters['search']) !== false) {
                                $includeCategory = true;
                                break;
                            }
                        }
                    }
                }

                // فلاتر الحقول
                if ($includeCategory) {
                    foreach($filters as $key => $value) {
                        if (strpos($key, 'filter_') === 0 && !empty($value)) {
                            $fieldName = str_replace('filter_', '', $key);
                            if (isset($category[$fieldName]) && $category[$fieldName] != $value) {
                                $includeCategory = false;
                                break;
                            }
                        }
                    }
                }

                if ($includeCategory) {
                    $results[] = $category;
                }
            }
        }

        // ترتيب النتائج
        if (!empty($filters['sort'])) {
            $sortField = $filters['sort'];
            $direction = (!empty($filters['direction']) && $filters['direction'] == 'desc') ? SORT_DESC : SORT_ASC;

            $sortValues = array_column($results, $sortField);
            array_multisort($sortValues, $direction, $results);
        }

        return $results;
    }

    /**
     * إنشاء فئة جديدة مع النظام الديناميكي الكامل
     * لا يعتمد على أي جداول ثابتة - فقط dynamic_field_values
     */
    public function createDynamicCategory($companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // إنشاء معرف فئة جديد
            $categoryId = $this->generateNewCategoryId($companyId);

            // حفظ جميع قيم الحقول في النظام الديناميكي
            $this->saveAllDynamicFieldValues($companyId, $categoryId, $data, $userId);

            $this->db->commit();
            return $categoryId;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * تحديث فئة مع النظام الديناميكي
     */
    public function updateDynamicCategory($categoryId, $companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // تحديث قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $categoryId, $data, $userId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على فئة مع قيمها الديناميكية
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicCategory($categoryId, $companyId, $fields = null)
    {
        // التحقق من وجود الفئة في النظام الديناميكي
        $checkSql = "SELECT COUNT(*) FROM dynamic_field_values
                     WHERE company_id = ? AND module_name = 'inventory'
                     AND table_name = 'categories' AND record_id = ?";
        $stmt = $this->db->prepare($checkSql);
        $stmt->execute([$companyId, $categoryId]);

        if ($stmt->fetchColumn() == 0) {
            return null;
        }

        // الحصول على الحقول إذا لم يتم تمريرها
        if ($fields === null) {
            $fields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'categories');
        }

        // إنشاء مصفوفة الفئة مع معرف الفئة
        $category = ['category_id' => $categoryId];

        // الحصول على قيم الحقول الديناميكية
        $fieldValues = $this->fieldManager->getFieldValues($companyId, 'inventory', 'categories', $categoryId);

        // دمج جميع القيم من النظام الديناميكي
        foreach($fields as $field) {
            $fieldName = $field['field_name'];
            $value = $fieldValues[$fieldName] ?? $field['default_value'] ?? '';
            $category[$fieldName] = $this->formatFieldValue($value, $field);
        }

        return $category;
    }

    // ========================================
    // الدوال المساعدة للنظام الديناميكي الكامل
    // ========================================

    /**
     * إنشاء معرف فئة جديد للشركة
     */
    private function generateNewCategoryId($companyId)
    {
        // الحصول على أعلى معرف فئة للشركة
        $sql = "SELECT MAX(record_id) FROM dynamic_field_values
                WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'categories'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId]);
        $maxId = $stmt->fetchColumn();

        return ($maxId ? $maxId + 1 : 1);
    }

    /**
     * التحقق من عدم تكرار كود الفئة
     */
    private function checkCategoryCodeUnique($companyId, $categoryCode, $excludeCategoryId = null)
    {
        // البحث عن كود الفئة في النظام الديناميكي
        $sql = "SELECT dfv.record_id
                FROM dynamic_field_values dfv
                JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'categories'
                AND sf.field_name = 'category_code'
                AND dfv.field_value = ?";

        $params = [$companyId, $categoryCode];

        if ($excludeCategoryId) {
            $sql .= " AND dfv.record_id != ?";
            $params[] = $excludeCategoryId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() === false;
    }

    /**
     * حفظ جميع قيم الحقول في النظام الديناميكي الكامل
     */
    private function saveAllDynamicFieldValues($companyId, $categoryId, $data, $userId)
    {
        // التحقق من كود الفئة
        if (empty($data['category_code'])) {
            throw new Exception('كود الفئة مطلوب');
        }

        if (!$this->checkCategoryCodeUnique($companyId, $data['category_code'])) {
            throw new Exception("كود الفئة '{$data['category_code']}' موجود مسبقاً");
        }

        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'categories');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            // تحديد القيمة
            $value = '';
            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);
            } elseif (!empty($field['default_value'])) {
                $value = $field['default_value'];
            } elseif ($field['is_required']) {
                throw new Exception("الحقل '{$field['field_label_ar']}' مطلوب");
            }

            // حفظ القيمة في النظام الديناميكي
            $this->fieldManager->saveFieldValue(
                $companyId,
                'inventory',
                'categories',
                $categoryId,
                $field['field_id'],
                $value,
                $userId
            );
        }
    }

    /**
     * تحديث قيم الحقول الديناميكية
     */
    private function saveDynamicFieldValues($companyId, $categoryId, $data, $userId)
    {
        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'categories');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);

                $this->fieldManager->saveFieldValue(
                    $companyId,
                    'inventory',
                    'categories',
                    $categoryId,
                    $field['field_id'],
                    $value,
                    $userId
                );
            }
        }
    }

    /**
     * معالجة قيمة الحقل قبل الحفظ
     */
    private function processFieldValue($value, $field)
    {
        switch($field['field_type']) {
            case 'INT':
                return (int)$value;

            case 'DECIMAL':
                return (float)$value;

            case 'TINYINT':
                return $value ? 1 : 0;

            case 'DATE':
                return !empty($value) ? date('Y-m-d', strtotime($value)) : null;

            case 'TIMESTAMP':
                return !empty($value) ? date('Y-m-d H:i:s', strtotime($value)) : null;

            default:
                return trim($value);
        }
    }

    /**
     * تنسيق قيمة الحقل للعرض
     */
    private function formatFieldValue($value, $field)
    {
        if (empty($value)) {
            return '';
        }

        switch($field['field_type']) {
            case 'DECIMAL':
                $precision = 2;
                if (!empty($field['field_length'])) {
                    $parts = explode(',', $field['field_length']);
                    if (count($parts) == 2) {
                        $precision = (int)$parts[1];
                    }
                }
                return number_format((float)$value, $precision);

            case 'TINYINT':
                if ($field['input_type'] == 'checkbox') {
                    return $value ? 'نعم' : 'لا';
                }
                return $value;

            case 'DATE':
                return date('Y-m-d', strtotime($value));

            case 'TIMESTAMP':
                return date('Y-m-d H:i:s', strtotime($value));

            default:
                return htmlspecialchars($value);
        }
    }

    /**
     * حذف فئة من النظام الديناميكي الكامل
     */
    public function deleteDynamicCategory($categoryId, $companyId)
    {
        try {
            $this->db->beginTransaction();

            // حذف جميع قيم الحقول الديناميكية للفئة
            $sql = "DELETE FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = 'inventory'
                    AND table_name = 'categories' AND record_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$companyId, $categoryId]);

            if (!$result) {
                throw new Exception('فشل في حذف الفئة');
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * البحث في الفئات الديناميكية
     */
    public function searchDynamicCategories($companyId, $searchTerm, $searchFields = null)
    {
        if ($searchFields === null) {
            $searchFields = $this->fieldManager->getCompanySearchableFields($companyId, 'inventory', 'categories');
        }

        if (empty($searchFields)) {
            return [];
        }

        // البحث في النظام الديناميكي الكامل
        $searchConditions = [];
        $params = [$companyId];

        foreach($searchFields as $field) {
            $searchConditions[] = "(dfv.field_id = ? AND dfv.field_value LIKE ?)";
            $params[] = $field['field_id'];
            $params[] = '%' . $searchTerm . '%';
        }

        $sql = "SELECT DISTINCT dfv.record_id
                FROM dynamic_field_values dfv
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'categories'
                AND (" . implode(' OR ', $searchConditions) . ")
                ORDER BY dfv.record_id DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $categoryIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // جلب بيانات الفئات المطابقة
        $results = [];
        foreach ($categoryIds as $categoryId) {
            $category = $this->getDynamicCategory($categoryId, $companyId);
            if ($category) {
                $results[] = $category;
            }
        }

        return $results;
    }

    /**
     * الحصول على الفئات للقائمة المنسدلة
     */
    public function getCategoriesForSelect($companyId)
    {
        // الحصول على الحقول الأساسية للفئات
        $basicFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'categories');

        // الحصول على الفئات النشطة فقط
        $categories = $this->getDynamicCategories($companyId, $basicFields, ['filter_is_active' => '1']);

        $result = [];
        foreach ($categories as $category) {
            $result[] = [
                'category_id' => $category['category_id'],
                'category_name_ar' => $category['category_name_ar'] ?? 'فئة',
                'category_code' => $category['category_code'] ?? ''
            ];
        }

        return $result;
    }

    /**
     * البحث عن فئة بالكود
     */
    public function getCategoryByCode($categoryCode, $companyId, $excludeCategoryId = null)
    {
        return !$this->checkCategoryCodeUnique($companyId, $categoryCode, $excludeCategoryId);
    }
}
