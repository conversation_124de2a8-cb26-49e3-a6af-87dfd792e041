<?php

namespace App\Modules\Inventory\Models;

use App\Core\Model;

class Category extends Model
{
    protected $table = 'dynamic_field_values';
    protected $module = 'inventory';
    protected $entityType = 'categories';

    /**
     * الحصول على جميع الفئات الديناميكية للشركة
     */
    public function getDynamicCategories($company_id, $fields = null)
    {
        try {
            // إذا لم يتم تحديد حقول، احصل على جميع الحقول المرئية
            if (!$fields) {
                $fieldManager = new \App\Core\FieldManager();
                $fields = $fieldManager->getCompanyTableFields($company_id, $this->module, $this->entityType);
            }

            if (empty($fields)) {
                return [];
            }

            // بناء استعلام ديناميكي
            $fieldNames = array_column($fields, 'field_name');
            $fieldIds = array_column($fields, 'field_id');
            
            $sql = "
                SELECT 
                    record_id as category_id,
                    " . $this->buildSelectFields($fieldNames) . "
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ? 
                AND dfv.module_name = ? 
                AND dfv.table_name = ?
                AND dfv.field_id IN (" . implode(',', array_fill(0, count($fieldIds), '?')) . ")
                GROUP BY record_id
                ORDER BY 
                    CASE WHEN sf.field_name = 'sort_order' THEN CAST(dfv.field_value AS UNSIGNED) END ASC,
                    CASE WHEN sf.field_name = 'category_name_ar' THEN dfv.field_value END ASC
            ";

            $params = array_merge(
                [$company_id, $this->module, $this->entityType],
                $fieldIds
            );

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في جلب الفئات الديناميكية: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على فئة واحدة بالمعرف
     */
    public function getDynamicCategory($category_id, $company_id)
    {
        try {
            $sql = "
                SELECT 
                    record_id as category_id,
                    sf.field_name,
                    dfv.field_value
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ? 
                AND dfv.module_name = ? 
                AND dfv.table_name = ?
                AND dfv.record_id = ?
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$company_id, $this->module, $this->entityType, $category_id]);
            $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            if (empty($rows)) {
                return null;
            }

            // تحويل النتائج إلى مصفوفة مفاتيح => قيم
            $category = ['category_id' => $category_id];
            foreach ($rows as $row) {
                $category[$row['field_name']] = $row['field_value'];
            }

            return $category;

        } catch (\Exception $e) {
            error_log("خطأ في جلب الفئة: " . $e->getMessage());
            return null;
        }
    }

    /**
     * إنشاء فئة جديدة
     */
    public function createDynamicCategory($company_id, $data, $user_id)
    {
        try {
            $this->db->beginTransaction();

            // الحصول على معرف جديد للفئة
            $category_id = $this->getNextRecordId($company_id);

            // الحصول على حقول الشركة
            $fieldManager = new \App\Core\FieldManager();
            $fields = $fieldManager->getCompanyFormFields($company_id, $this->module, $this->entityType);

            // إدراج قيم الحقول
            foreach ($fields as $field) {
                $fieldName = $field['field_name'];
                $fieldValue = $data[$fieldName] ?? $field['default_value'] ?? '';

                // تخطي الحقول الفارغة غير المطلوبة
                if (empty($fieldValue) && !$field['is_required']) {
                    continue;
                }

                // إدراج القيمة
                $sql = "
                    INSERT INTO dynamic_field_values 
                    (company_id, module_name, table_name, record_id, field_id, field_value, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ";

                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $company_id,
                    $this->module,
                    $this->entityType,
                    $category_id,
                    $field['field_id'],
                    $fieldValue,
                    $user_id
                ]);
            }

            $this->db->commit();
            return $category_id;

        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("خطأ في إنشاء الفئة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث فئة موجودة
     */
    public function updateDynamicCategory($category_id, $company_id, $data, $user_id)
    {
        try {
            $this->db->beginTransaction();

            // الحصول على حقول الشركة
            $fieldManager = new \App\Core\FieldManager();
            $fields = $fieldManager->getCompanyFormFields($company_id, $this->module, $this->entityType);

            foreach ($fields as $field) {
                $fieldName = $field['field_name'];
                $fieldValue = $data[$fieldName] ?? '';

                // التحقق من وجود القيمة مسبقاً
                $sql = "
                    SELECT id FROM dynamic_field_values 
                    WHERE company_id = ? AND module_name = ? AND table_name = ? 
                    AND record_id = ? AND field_id = ?
                ";
                
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$company_id, $this->module, $this->entityType, $category_id, $field['field_id']]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // تحديث القيمة الموجودة
                    $sql = "
                        UPDATE dynamic_field_values 
                        SET field_value = ?, updated_by = ?, updated_at = NOW()
                        WHERE id = ?
                    ";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([$fieldValue, $user_id, $existing['id']]);
                } else {
                    // إدراج قيمة جديدة
                    $sql = "
                        INSERT INTO dynamic_field_values 
                        (company_id, module_name, table_name, record_id, field_id, field_value, created_by, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                    ";
                    $stmt = $this->db->prepare($sql);
                    $stmt->execute([
                        $company_id,
                        $this->module,
                        $this->entityType,
                        $category_id,
                        $field['field_id'],
                        $fieldValue,
                        $user_id
                    ]);
                }
            }

            $this->db->commit();
            return true;

        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("خطأ في تحديث الفئة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف فئة
     */
    public function deleteDynamicCategory($category_id, $company_id)
    {
        try {
            $sql = "
                DELETE FROM dynamic_field_values 
                WHERE company_id = ? AND module_name = ? AND table_name = ? AND record_id = ?
            ";

            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$company_id, $this->module, $this->entityType, $category_id]);

        } catch (\Exception $e) {
            error_log("خطأ في حذف الفئة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * البحث عن فئة بالكود
     */
    public function getCategoryByCode($code, $company_id, $exclude_id = null)
    {
        try {
            $sql = "
                SELECT dfv.record_id as category_id
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ? 
                AND dfv.module_name = ? 
                AND dfv.table_name = ?
                AND sf.field_name = 'category_code'
                AND dfv.field_value = ?
            ";

            $params = [$company_id, $this->module, $this->entityType, $code];

            if ($exclude_id) {
                $sql .= " AND dfv.record_id != ?";
                $params[] = $exclude_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في البحث عن الفئة بالكود: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على معرف جديد للسجل
     */
    private function getNextRecordId($company_id)
    {
        $sql = "
            SELECT COALESCE(MAX(record_id), 0) + 1 as next_id
            FROM dynamic_field_values 
            WHERE company_id = ? AND module_name = ? AND table_name = ?
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->module, $this->entityType]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['next_id'] ?? 1;
    }

    /**
     * بناء حقول SELECT للاستعلام الديناميكي
     */
    private function buildSelectFields($fieldNames)
    {
        $selectFields = [];
        
        foreach ($fieldNames as $fieldName) {
            $selectFields[] = "MAX(CASE WHEN sf.field_name = '{$fieldName}' THEN dfv.field_value END) as {$fieldName}";
        }

        return implode(', ', $selectFields);
    }

    /**
     * البحث في الفئات الديناميكية
     */
    public function searchDynamicCategories($company_id, $searchTerm, $fields = null)
    {
        try {
            // إذا لم يتم تحديد حقول، احصل على جميع الحقول المرئية
            if (!$fields) {
                $fieldManager = new \App\Core\FieldManager();
                $fields = $fieldManager->getCompanyTableFields($company_id, $this->module, $this->entityType);
            }

            if (empty($fields) || empty($searchTerm)) {
                return $this->getDynamicCategories($company_id, $fields);
            }

            // بناء استعلام البحث الديناميكي
            $fieldNames = array_column($fields, 'field_name');
            $fieldIds = array_column($fields, 'field_id');

            // بناء شروط البحث
            $searchConditions = [];
            foreach ($fieldNames as $fieldName) {
                if (in_array($fieldName, ['category_name_ar', 'category_name_en', 'category_code', 'description_ar', 'description_en'])) {
                    $searchConditions[] = "(sf.field_name = '{$fieldName}' AND dfv.field_value LIKE ?)";
                }
            }

            if (empty($searchConditions)) {
                return $this->getDynamicCategories($company_id, $fields);
            }

            $sql = "
                SELECT
                    record_id as category_id,
                    " . $this->buildSelectFields($fieldNames) . "
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = ?
                AND dfv.table_name = ?
                AND dfv.field_id IN (" . implode(',', array_fill(0, count($fieldIds), '?')) . ")
                AND dfv.record_id IN (
                    SELECT DISTINCT record_id
                    FROM dynamic_field_values dfv2
                    INNER JOIN system_fields sf2 ON dfv2.field_id = sf2.field_id
                    WHERE dfv2.company_id = ?
                    AND dfv2.module_name = ?
                    AND dfv2.table_name = ?
                    AND (" . implode(' OR ', $searchConditions) . ")
                )
                GROUP BY record_id
                ORDER BY
                    CASE WHEN sf.field_name = 'sort_order' THEN CAST(dfv.field_value AS UNSIGNED) END ASC,
                    CASE WHEN sf.field_name = 'category_name_ar' THEN dfv.field_value END ASC
            ";

            $params = array_merge(
                [$company_id, $this->module, $this->entityType],
                $fieldIds,
                [$company_id, $this->module, $this->entityType],
                array_fill(0, count($searchConditions), "%{$searchTerm}%")
            );

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في البحث في الفئات الديناميكية: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على الفئات للقائمة المنسدلة
     */
    public function getCategoriesForSelect($company_id)
    {
        try {
            $sql = "
                SELECT
                    dfv.record_id as category_id,
                    MAX(CASE WHEN sf.field_name = 'category_name_ar' THEN dfv.field_value END) as category_name_ar,
                    MAX(CASE WHEN sf.field_name = 'sort_order' THEN CAST(dfv.field_value AS UNSIGNED) END) as sort_order
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = ?
                AND dfv.table_name = ?
                AND sf.field_name IN ('category_name_ar', 'sort_order', 'is_active')
                GROUP BY dfv.record_id
                HAVING MAX(CASE WHEN sf.field_name = 'is_active' THEN dfv.field_value END) = '1'
                ORDER BY sort_order ASC, category_name_ar ASC
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$company_id, $this->module, $this->entityType]);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في جلب فئات القائمة المنسدلة: " . $e->getMessage());
            return [];
        }
    }
}
