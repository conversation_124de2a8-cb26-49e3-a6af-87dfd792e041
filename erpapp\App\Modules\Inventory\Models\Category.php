<?php

namespace App\Modules\Inventory\Models;

use PDO;
use Exception;
use App\Core\FieldManager;

/**
 * Category Model - نموذج الفئات
 * النظام الديناميكي الكامل - مطابق لنمط Product Model
 */
class Category
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Field manager for dynamic fields
     */
    protected $fieldManager;

    /**
     * Module and table constants
     */
    protected $module = 'inventory';
    protected $entityType = 'categories';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->fieldManager = new FieldManager($db);
    }

    /**
     * الحصول على الفئات مع القيم الديناميكية للعرض في الجداول
     * النظام الديناميكي الكامل - مطابق لنمط Product Model
     */
    public function getDynamicCategories($companyId, $tableFields = null, $filters = [])
    {
        // الحصول على معرفات الفئات الفريدة للشركة
        $categoryIdsSql = "SELECT DISTINCT record_id
                          FROM dynamic_field_values
                          WHERE company_id = ? AND module_name = ? AND table_name = ?
                          ORDER BY record_id DESC";

        if (!empty($filters['limit'])) {
            $categoryIdsSql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($categoryIdsSql);
        $stmt->execute([$companyId, $this->module, $this->entityType]);
        $categoryIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($categoryIds)) {
            return [];
        }

        $results = [];

        // جلب بيانات كل فئة
        foreach ($categoryIds as $categoryId) {
            $category = $this->getDynamicCategory($categoryId, $companyId, $tableFields);
            if ($category) {
                // تطبيق الفلاتر
                $includeCategory = true;

                // فلتر البحث
                if (!empty($filters['search'])) {
                    $includeCategory = false;
                    if ($tableFields) {
                        foreach($tableFields as $field) {
                            if ($field['is_searchable'] && isset($category[$field['field_name']])) {
                                if (stripos($category[$field['field_name']], $filters['search']) !== false) {
                                    $includeCategory = true;
                                    break;
                                }
                            }
                        }
                    }
                }

                // فلاتر الحقول
                if ($includeCategory) {
                    foreach($filters as $key => $value) {
                        if (strpos($key, 'filter_') === 0 && !empty($value)) {
                            $fieldName = str_replace('filter_', '', $key);
                            if (isset($category[$fieldName]) && $category[$fieldName] != $value) {
                                $includeCategory = false;
                                break;
                            }
                        }
                    }
                }

                if ($includeCategory) {
                    $results[] = $category;
                }
            }
        }

        // ترتيب النتائج
        if (!empty($filters['sort'])) {
            $sortField = $filters['sort'];
            $direction = (!empty($filters['direction']) && $filters['direction'] == 'desc') ? SORT_DESC : SORT_ASC;

            $sortValues = array_column($results, $sortField);
            array_multisort($sortValues, $direction, $results);
        }

        return $results;
    }

    /**
     * الحصول على فئة مع قيمها الديناميكية
     * النظام الديناميكي الكامل - مطابق لنمط Product Model
     */
    public function getDynamicCategory($categoryId, $companyId, $fields = null)
    {
        // التحقق من وجود الفئة في النظام الديناميكي
        $checkSql = "SELECT COUNT(*) FROM dynamic_field_values
                     WHERE company_id = ? AND module_name = ?
                     AND table_name = ? AND record_id = ?";
        $stmt = $this->db->prepare($checkSql);
        $stmt->execute([$companyId, $this->module, $this->entityType, $categoryId]);

        if ($stmt->fetchColumn() == 0) {
            return null;
        }

        // الحصول على الحقول إذا لم يتم تمريرها
        if ($fields === null) {
            $fields = $this->fieldManager->getCompanySelectedFields($companyId, $this->module, $this->entityType);
        }

        // إنشاء مصفوفة الفئة مع معرف الفئة
        $category = ['category_id' => $categoryId];

        // الحصول على قيم الحقول الديناميكية
        $fieldValues = $this->fieldManager->getFieldValues($companyId, $this->module, $this->entityType, $categoryId);

        // دمج جميع القيم من النظام الديناميكي
        foreach($fields as $field) {
            $fieldName = $field['field_name'];
            $value = $fieldValues[$fieldName] ?? $field['default_value'] ?? '';
            $category[$fieldName] = $this->formatFieldValue($value, $field);
        }

        return $category;
    }

    /**
     * إنشاء فئة جديدة مع النظام الديناميكي الكامل
     * مطابق لنمط Product Model
     */
    public function createDynamicCategory($companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // إنشاء معرف فئة جديد
            $categoryId = $this->generateNewCategoryId($companyId);

            // حفظ جميع قيم الحقول في النظام الديناميكي
            $this->saveAllDynamicFieldValues($companyId, $categoryId, $data, $userId);

            $this->db->commit();
            return $categoryId;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * تحديث فئة مع النظام الديناميكي
     * مطابق لنمط Product Model
     */
    public function updateDynamicCategory($categoryId, $companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // تحديث قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $categoryId, $data, $userId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * حذف فئة من النظام الديناميكي الكامل
     * مطابق لنمط Product Model
     */
    public function deleteDynamicCategory($categoryId, $companyId)
    {
        try {
            $this->db->beginTransaction();

            // حذف جميع قيم الحقول الديناميكية للفئة
            $sql = "DELETE FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = ?
                    AND table_name = ? AND record_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$companyId, $this->module, $this->entityType, $categoryId]);

            if (!$result) {
                throw new Exception('فشل في حذف الفئة');
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * البحث عن فئة بالكود
     */
    public function getCategoryByCode($code, $company_id, $exclude_id = null)
    {
        try {
            $sql = "
                SELECT dfv.record_id as category_id
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ? 
                AND dfv.module_name = ? 
                AND dfv.table_name = ?
                AND sf.field_name = 'category_code'
                AND dfv.field_value = ?
            ";

            $params = [$company_id, $this->module, $this->entityType, $code];

            if ($exclude_id) {
                $sql .= " AND dfv.record_id != ?";
                $params[] = $exclude_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في البحث عن الفئة بالكود: " . $e->getMessage());
            return null;
        }
    }

    // ========================================
    // الدوال المساعدة للنظام الديناميكي الكامل
    // مطابقة لنمط Product Model
    // ========================================

    /**
     * إنشاء معرف فئة جديد للشركة
     */
    private function generateNewCategoryId($companyId)
    {
        // الحصول على أعلى معرف فئة للشركة
        $sql = "SELECT MAX(record_id) FROM dynamic_field_values
                WHERE company_id = ? AND module_name = ? AND table_name = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $this->module, $this->entityType]);
        $maxId = $stmt->fetchColumn();

        return ($maxId ? $maxId + 1 : 1);
    }

    /**
     * التحقق من عدم تكرار كود الفئة
     */
    private function checkCategoryCodeUnique($companyId, $categoryCode, $excludeCategoryId = null)
    {
        // البحث عن كود الفئة في النظام الديناميكي
        $sql = "SELECT dfv.record_id
                FROM dynamic_field_values dfv
                JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = ?
                AND dfv.table_name = ?
                AND sf.field_name = 'category_code'
                AND dfv.field_value = ?";

        $params = [$companyId, $this->module, $this->entityType, $categoryCode];

        if ($excludeCategoryId) {
            $sql .= " AND dfv.record_id != ?";
            $params[] = $excludeCategoryId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() === false;
    }

    /**
     * حفظ جميع قيم الحقول في النظام الديناميكي الكامل
     */
    private function saveAllDynamicFieldValues($companyId, $categoryId, $data, $userId)
    {
        // التحقق من كود الفئة
        if (empty($data['category_code'])) {
            throw new Exception('كود الفئة مطلوب');
        }

        if (!$this->checkCategoryCodeUnique($companyId, $data['category_code'])) {
            throw new Exception("كود الفئة '{$data['category_code']}' موجود مسبقاً");
        }

        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, $this->module, $this->entityType);

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            // تحديد القيمة
            $value = '';
            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);
            } elseif (!empty($field['default_value'])) {
                $value = $field['default_value'];
            } elseif ($field['is_required']) {
                throw new Exception("الحقل '{$field['field_label_ar']}' مطلوب");
            }

            // حفظ القيمة في النظام الديناميكي
            $this->fieldManager->saveFieldValue(
                $companyId,
                $this->module,
                $this->entityType,
                $categoryId,
                $field['field_id'],
                $value,
                $userId
            );
        }
    }

    /**
     * تحديث قيم الحقول الديناميكية
     */
    private function saveDynamicFieldValues($companyId, $categoryId, $data, $userId)
    {
        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, $this->module, $this->entityType);

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);

                $this->fieldManager->saveFieldValue(
                    $companyId,
                    $this->module,
                    $this->entityType,
                    $categoryId,
                    $field['field_id'],
                    $value,
                    $userId
                );
            }
        }
    }

    /**
     * معالجة قيمة الحقل قبل الحفظ
     */
    private function processFieldValue($value, $field)
    {
        switch($field['field_type']) {
            case 'INT':
                return (int)$value;

            case 'DECIMAL':
                return (float)$value;

            case 'TINYINT':
                return $value ? 1 : 0;

            case 'DATE':
                return !empty($value) ? date('Y-m-d', strtotime($value)) : null;

            case 'TIMESTAMP':
                return !empty($value) ? date('Y-m-d H:i:s', strtotime($value)) : null;

            default:
                return trim($value);
        }
    }

    /**
     * تنسيق قيمة الحقل للعرض
     */
    private function formatFieldValue($value, $field)
    {
        if (empty($value)) {
            return '';
        }

        switch($field['field_type']) {
            case 'DECIMAL':
                $precision = 2;
                if (!empty($field['field_length'])) {
                    $parts = explode(',', $field['field_length']);
                    if (count($parts) == 2) {
                        $precision = (int)$parts[1];
                    }
                }
                return number_format((float)$value, $precision);

            case 'TINYINT':
                if ($field['input_type'] == 'checkbox') {
                    return $value ? 'نعم' : 'لا';
                }
                return $value;

            case 'DATE':
                return date('Y-m-d', strtotime($value));

            case 'TIMESTAMP':
                return date('Y-m-d H:i:s', strtotime($value));

            default:
                return htmlspecialchars($value);
        }
    }

    /**
     * البحث في الفئات الديناميكية
     */
    public function searchDynamicCategories($company_id, $searchTerm, $fields = null)
    {
        try {
            // إذا لم يتم تحديد حقول، احصل على جميع الحقول المرئية
            if (!$fields) {
                $fieldManager = new \App\Core\FieldManager();
                $fields = $fieldManager->getCompanyTableFields($company_id, $this->module, $this->entityType);
            }

            if (empty($fields) || empty($searchTerm)) {
                return $this->getDynamicCategories($company_id, $fields);
            }

            // بناء استعلام البحث الديناميكي
            $fieldNames = array_column($fields, 'field_name');
            $fieldIds = array_column($fields, 'field_id');

            // بناء شروط البحث
            $searchConditions = [];
            foreach ($fieldNames as $fieldName) {
                if (in_array($fieldName, ['category_name_ar', 'category_name_en', 'category_code', 'description_ar', 'description_en'])) {
                    $searchConditions[] = "(sf.field_name = '{$fieldName}' AND dfv.field_value LIKE ?)";
                }
            }

            if (empty($searchConditions)) {
                return $this->getDynamicCategories($company_id, $fields);
            }

            $sql = "
                SELECT
                    record_id as category_id,
                    " . $this->buildSelectFields($fieldNames) . "
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = ?
                AND dfv.table_name = ?
                AND dfv.field_id IN (" . implode(',', array_fill(0, count($fieldIds), '?')) . ")
                AND dfv.record_id IN (
                    SELECT DISTINCT record_id
                    FROM dynamic_field_values dfv2
                    INNER JOIN system_fields sf2 ON dfv2.field_id = sf2.field_id
                    WHERE dfv2.company_id = ?
                    AND dfv2.module_name = ?
                    AND dfv2.table_name = ?
                    AND (" . implode(' OR ', $searchConditions) . ")
                )
                GROUP BY record_id
                ORDER BY
                    CASE WHEN sf.field_name = 'sort_order' THEN CAST(dfv.field_value AS UNSIGNED) END ASC,
                    CASE WHEN sf.field_name = 'category_name_ar' THEN dfv.field_value END ASC
            ";

            $params = array_merge(
                [$company_id, $this->module, $this->entityType],
                $fieldIds,
                [$company_id, $this->module, $this->entityType],
                array_fill(0, count($searchConditions), "%{$searchTerm}%")
            );

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في البحث في الفئات الديناميكية: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على الفئات للقائمة المنسدلة
     */
    public function getCategoriesForSelect($company_id)
    {
        try {
            $sql = "
                SELECT
                    dfv.record_id as category_id,
                    MAX(CASE WHEN sf.field_name = 'category_name_ar' THEN dfv.field_value END) as category_name_ar,
                    MAX(CASE WHEN sf.field_name = 'sort_order' THEN CAST(dfv.field_value AS UNSIGNED) END) as sort_order
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = ?
                AND dfv.table_name = ?
                AND sf.field_name IN ('category_name_ar', 'sort_order', 'is_active')
                GROUP BY dfv.record_id
                HAVING MAX(CASE WHEN sf.field_name = 'is_active' THEN dfv.field_value END) = '1'
                ORDER BY sort_order ASC, category_name_ar ASC
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$company_id, $this->module, $this->entityType]);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (\Exception $e) {
            error_log("خطأ في جلب فئات القائمة المنسدلة: " . $e->getMessage());
            return [];
        }
    }
}
