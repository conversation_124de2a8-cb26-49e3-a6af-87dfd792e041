<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;
use App\Core\FieldManager;

/**
 * Product Model - نموذج المنتجات
 * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
 */
class Product
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Field manager for dynamic fields
     */
    protected $fieldManager;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->fieldManager = new FieldManager($db);
    }

    // ========================================
    // الدوال الديناميكية الجديدة
    // ========================================



    /**
     * الحصول على المنتجات مع القيم الديناميكية للعرض في الجداول
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicProducts($companyId, $tableFields, $filters = [])
    {
        // الحصول على معرفات المنتجات الفريدة للشركة
        $productIdsSql = "SELECT DISTINCT record_id
                         FROM dynamic_field_values
                         WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'products'
                         ORDER BY record_id DESC";

        if (!empty($filters['limit'])) {
            $productIdsSql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($productIdsSql);
        $stmt->execute([$companyId]);
        $productIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($productIds)) {
            return [];
        }

        $results = [];

        // جلب بيانات كل منتج
        foreach ($productIds as $productId) {
            $product = $this->getDynamicProduct($productId, $companyId, $tableFields);
            if ($product) {
                // تطبيق الفلاتر
                $includeProduct = true;

                // فلتر البحث
                if (!empty($filters['search'])) {
                    $includeProduct = false;
                    foreach($tableFields as $field) {
                        if ($field['is_searchable'] && isset($product[$field['field_name']])) {
                            if (stripos($product[$field['field_name']], $filters['search']) !== false) {
                                $includeProduct = true;
                                break;
                            }
                        }
                    }
                }

                // فلاتر الحقول
                if ($includeProduct) {
                    foreach($filters as $key => $value) {
                        if (strpos($key, 'filter_') === 0 && !empty($value)) {
                            $fieldName = str_replace('filter_', '', $key);
                            if (isset($product[$fieldName]) && $product[$fieldName] != $value) {
                                $includeProduct = false;
                                break;
                            }
                        }
                    }
                }

                if ($includeProduct) {
                    $results[] = $product;
                }
            }
        }

        // ترتيب النتائج
        if (!empty($filters['sort'])) {
            $sortField = $filters['sort'];
            $direction = (!empty($filters['direction']) && $filters['direction'] == 'desc') ? SORT_DESC : SORT_ASC;

            $sortValues = array_column($results, $sortField);
            array_multisort($sortValues, $direction, $results);
        }

        return $results;
    }

    /**
     * إنشاء منتج جديد مع النظام الديناميكي الكامل
     * لا يعتمد على أي جداول ثابتة - فقط dynamic_field_values
     */
    public function createDynamicProduct($companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // إنشاء معرف منتج جديد
            $productId = $this->generateNewProductId($companyId);

            // حفظ جميع قيم الحقول في النظام الديناميكي
            $this->saveAllDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return $productId;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * تحديث منتج مع النظام الديناميكي
     */
    public function updateDynamicProduct($productId, $companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // تحديث قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على منتج مع قيمه الديناميكية
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicProduct($productId, $companyId, $fields = null)
    {
        // التحقق من وجود المنتج في النظام الديناميكي
        $checkSql = "SELECT COUNT(*) FROM dynamic_field_values
                     WHERE company_id = ? AND module_name = 'inventory'
                     AND table_name = 'products' AND record_id = ?";
        $stmt = $this->db->prepare($checkSql);
        $stmt->execute([$companyId, $productId]);

        if ($stmt->fetchColumn() == 0) {
            return null;
        }

        // الحصول على الحقول إذا لم يتم تمريرها
        if ($fields === null) {
            $fields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');
        }

        // إنشاء مصفوفة المنتج مع معرف المنتج
        $product = ['product_id' => $productId];

        // الحصول على قيم الحقول الديناميكية
        $fieldValues = $this->fieldManager->getFieldValues($companyId, 'inventory', 'products', $productId);

        // دمج جميع القيم من النظام الديناميكي
        foreach($fields as $field) {
            $fieldName = $field['field_name'];
            $value = $fieldValues[$fieldName] ?? $field['default_value'] ?? '';
            $product[$fieldName] = $this->formatFieldValue($value, $field);
        }

        return $product;
    }

    // ========================================
    // الدوال المساعدة للنظام الديناميكي الكامل
    // ========================================

    /**
     * إنشاء معرف منتج جديد للشركة
     */
    private function generateNewProductId($companyId)
    {
        // الحصول على أعلى معرف منتج للشركة
        $sql = "SELECT MAX(record_id) FROM dynamic_field_values
                WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'products'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId]);
        $maxId = $stmt->fetchColumn();

        return ($maxId ? $maxId + 1 : 1);
    }

    /**
     * التحقق من عدم تكرار كود المنتج
     */
    private function checkProductCodeUnique($companyId, $productCode, $excludeProductId = null)
    {
        // البحث عن كود المنتج في النظام الديناميكي
        $sql = "SELECT dfv.record_id
                FROM dynamic_field_values dfv
                JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'products'
                AND sf.field_name = 'product_code'
                AND dfv.field_value = ?";

        $params = [$companyId, $productCode];

        if ($excludeProductId) {
            $sql .= " AND dfv.record_id != ?";
            $params[] = $excludeProductId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() === false;
    }

    /**
     * حفظ جميع قيم الحقول في النظام الديناميكي الكامل
     */
    private function saveAllDynamicFieldValues($companyId, $productId, $data, $userId)
    {
        // التحقق من كود المنتج
        if (empty($data['product_code'])) {
            throw new Exception('كود المنتج مطلوب');
        }

        if (!$this->checkProductCodeUnique($companyId, $data['product_code'])) {
            throw new Exception("كود المنتج '{$data['product_code']}' موجود مسبقاً");
        }

        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            // تحديد القيمة
            $value = '';
            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);
            } elseif (!empty($field['default_value'])) {
                $value = $field['default_value'];
            } elseif ($field['is_required']) {
                throw new Exception("الحقل '{$field['field_label_ar']}' مطلوب");
            }

            // حفظ القيمة في النظام الديناميكي
            $this->fieldManager->saveFieldValue(
                $companyId,
                'inventory',
                'products',
                $productId,
                $field['field_id'],
                $value,
                $userId
            );
        }
    }

    /**
     * تحديث قيم الحقول الديناميكية
     */
    private function saveDynamicFieldValues($companyId, $productId, $data, $userId)
    {
        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);

                $this->fieldManager->saveFieldValue(
                    $companyId,
                    'inventory',
                    'products',
                    $productId,
                    $field['field_id'],
                    $value,
                    $userId
                );
            }
        }
    }

    /**
     * معالجة قيمة الحقل قبل الحفظ
     */
    private function processFieldValue($value, $field)
    {
        switch($field['field_type']) {
            case 'INT':
                return (int)$value;

            case 'DECIMAL':
                return (float)$value;

            case 'TINYINT':
                return $value ? 1 : 0;

            case 'DATE':
                return !empty($value) ? date('Y-m-d', strtotime($value)) : null;

            case 'TIMESTAMP':
                return !empty($value) ? date('Y-m-d H:i:s', strtotime($value)) : null;

            default:
                return trim($value);
        }
    }

    /**
     * تنسيق قيمة الحقل للعرض
     */
    private function formatFieldValue($value, $field)
    {
        if (empty($value)) {
            return '';
        }

        switch($field['field_type']) {
            case 'DECIMAL':
                $precision = 2;
                if (!empty($field['field_length'])) {
                    $parts = explode(',', $field['field_length']);
                    if (count($parts) == 2) {
                        $precision = (int)$parts[1];
                    }
                }
                return number_format((float)$value, $precision);

            case 'TINYINT':
                if ($field['input_type'] == 'checkbox') {
                    return $value ? 'نعم' : 'لا';
                }
                return $value;

            case 'DATE':
                return date('Y-m-d', strtotime($value));

            case 'TIMESTAMP':
                return date('Y-m-d H:i:s', strtotime($value));

            default:
                return htmlspecialchars($value);
        }
    }

    /**
     * حذف منتج من النظام الديناميكي الكامل
     */
    public function deleteDynamicProduct($productId, $companyId)
    {
        try {
            $this->db->beginTransaction();

            // حذف جميع قيم الحقول الديناميكية للمنتج
            $sql = "DELETE FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = 'inventory'
                    AND table_name = 'products' AND record_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$companyId, $productId]);

            if (!$result) {
                throw new Exception('فشل في حذف المنتج');
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * البحث في المنتجات الديناميكية
     */
    public function searchDynamicProducts($companyId, $searchTerm, $searchFields = null)
    {
        if ($searchFields === null) {
            $searchFields = $this->fieldManager->getCompanySearchableFields($companyId, 'inventory', 'products');
        }

        if (empty($searchFields)) {
            return [];
        }

        // البحث في النظام الديناميكي الكامل
        $searchConditions = [];
        $params = [$companyId];

        foreach($searchFields as $field) {
            $searchConditions[] = "(dfv.field_id = ? AND dfv.field_value LIKE ?)";
            $params[] = $field['field_id'];
            $params[] = '%' . $searchTerm . '%';
        }

        $sql = "SELECT DISTINCT dfv.record_id
                FROM dynamic_field_values dfv
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'products'
                AND (" . implode(' OR ', $searchConditions) . ")
                ORDER BY dfv.record_id DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $productIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // جلب بيانات المنتجات المطابقة
        $results = [];
        foreach ($productIds as $productId) {
            $product = $this->getDynamicProduct($productId, $companyId);
            if ($product) {
                $results[] = $product;
            }
        }

        return $results;
    }
}
