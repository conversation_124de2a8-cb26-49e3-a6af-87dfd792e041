<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Models\Product;
use App\Modules\Inventory\Models\Category;
use App\Modules\Inventory\Models\Unit;
use App\Core\FieldManager;

/**
 * Product Controller - متحكم المنتجات
 */
class ProductController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Product model
     */
    protected $productModel;

    /**
     * Field manager
     */
    protected $fieldManager;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->productModel = new Product();

        // إنشاء مدير الحقول الديناميكية
        global $db;
        $this->fieldManager = new FieldManager($db);

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض قائمة المنتجات (ديناميكي)
     */
    public function index()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'products');

            if (!$hasFieldSettings) {
                // توجيه الشركة لإعداد الحقول أولاً
                view('Inventory::products/setup_required', [
                    'title' => 'إعداد حقول المنتجات مطلوب',
                    'message' => 'يجب إعداد حقول المنتجات أولاً قبل البدء في استخدام النظام',
                    'setup_url' => base_url('inventory/settings/fields')
                ]);
                return;
            }

            // الحصول على الحقول المرئية في الجداول
            $tableFields = $this->fieldManager->getCompanyTableFields($company_id, 'inventory', 'products');

            // الحصول على الحقول القابلة للبحث والفلترة
            $searchableFields = $this->fieldManager->getCompanySearchableFields($company_id, 'inventory', 'products');
            $filterableFields = $this->fieldManager->getCompanyFilterableFields($company_id, 'inventory', 'products');

            // الحصول على المنتجات مع القيم الديناميكية
            $products = $this->productModel->getDynamicProducts($company_id, $tableFields, $_GET);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إدارة المنتجات - نظام ديناميكي',
                'products' => $products,
                'tableFields' => $tableFields,
                'searchableFields' => $searchableFields,
                'filterableFields' => $filterableFields,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::products/index', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ أثناء تحميل المنتجات: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory'));
        }
    }

    /**
     * عرض نموذج إضافة منتج جديد (ديناميكي)
     */
    public function create()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'products');

            if (!$hasFieldSettings) {
                redirect(base_url('inventory/settings/fields?message=setup_required'));
                return;
            }

            // الحصول على التبويبات والحقول المرئية في النماذج
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'products', 'form');

            // الحصول على الحقول المطلوبة
            $requiredFields = $this->fieldManager->getCompanyRequiredFields($company_id, 'inventory', 'products');
            $requiredFieldNames = array_column($requiredFields, 'field_name');

            // الحصول على البيانات المساعدة للحقول المرتبطة
            $categories = [];
            $units = [];

            // التحقق من وجود حقول الفئات والوحدات في الحقول المختارة
            foreach($tabs as $tab) {
                foreach($tab['fields'] as $field) {
                    if ($field['field_name'] == 'category_id') {
                        $categoryModel = new Category();
                        $categories = $categoryModel->getByCompany($company_id);
                    }
                    if ($field['field_name'] == 'unit_id') {
                        $unitModel = new Unit();
                        $units = $unitModel->getByCompany($company_id);
                    }
                }
            }

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إضافة منتج جديد - نظام ديناميكي',
                'tabs' => $tabs,
                'requiredFields' => $requiredFieldNames,
                'categories' => $categories,
                'units' => $units,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => 'إضافة منتج', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::products/create', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ أثناء تحميل النموذج: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * حفظ منتج جديد (ديناميكي)
     */
    public function store()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/products/create'));
            }

            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            // التحقق من البيانات الديناميكية
            $this->validateDynamicProductData($_POST, $company_id);

            // إنشاء المنتج باستخدام النظام الديناميكي
            $product_id = $this->productModel->createDynamicProduct($company_id, $_POST, $user_id);

            if ($product_id) {
                flash('product_success', 'تم إضافة المنتج بنجاح', 'success');

                // التحقق من وجود زر "حفظ وإضافة آخر"
                if (isset($_POST['save_and_new'])) {
                    redirect(base_url('inventory/products/create'));
                } else {
                    redirect(base_url('inventory/products'));
                }
            } else {
                flash('product_error', 'حدث خطأ أثناء إضافة المنتج', 'danger');
                redirect(base_url('inventory/products/create'));
            }

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products/create'));
        }
    }

    /**
     * الحفظ التلقائي للنموذج
     */
    public function autoSave()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'message' => 'طريقة غير صحيحة']);
                return;
            }

            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            // حفظ البيانات في الجلسة للاستخدام لاحقاً
            $_SESSION['auto_save_data'] = $_POST;
            $_SESSION['auto_save_time'] = time();

            echo json_encode([
                'success' => true,
                'message' => 'تم الحفظ التلقائي',
                'timestamp' => date('H:i:s')
            ]);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * عرض تفاصيل منتج (ديناميكي)
     */
    public function show()
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];

            if (!$product_id) {
                flash('product_error', 'معرف المنتج مطلوب', 'danger');
                redirect(base_url('inventory/products'));
                return;
            }

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'products');

            if (!$hasFieldSettings) {
                redirect(base_url('inventory/settings/fields?message=setup_required'));
                return;
            }

            // الحصول على الحقول المرئية في صفحة التفاصيل
            $detailFields = $this->fieldManager->getCompanyDetailFields($company_id, 'inventory', 'products');

            // تجميع الحقول حسب التبويبات
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'products', 'details');

            // الحصول على بيانات المنتج
            $product = $this->productModel->getDynamicProduct($product_id, $company_id, $detailFields);

            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
                return;
            }

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'تفاصيل المنتج - ' . ($product['product_name_ar'] ?? 'منتج'),
                'product' => $product,
                'tabs' => $tabs,
                'detailFields' => $detailFields,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => 'تفاصيل المنتج', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::products/show', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ أثناء تحميل المنتج: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * عرض نموذج تعديل منتج
     */
    public function edit()
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if (!$product_id) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            $product = $this->productModel->getById($product_id, $company_id);

            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // الحصول على البيانات المساعدة
            $categories = $this->productService->getCategories($company_id);
            $units = $this->productService->getUnits($company_id);

            $data = [
                'title' => 'تعديل المنتج - ' . $product['product_name_ar'],
                'product' => $product,
                'categories' => $categories,
                'units' => $units,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => 'تعديل المنتج', 'active' => true]
                ]
            ];

            view('Inventory::products/edit', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * تحديث منتج
     */
    public function update()
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$product_id) {
                redirect(base_url('inventory/products'));
            }

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // التحقق من البيانات
            $productData = $this->validateProductData($_POST);
            $productData['updated_by'] = $user_id;

            // تحديث المنتج
            if ($this->productModel->update($product_id, $productData, $company_id)) {
                flash('product_success', 'تم تحديث المنتج بنجاح', 'success');
                redirect(base_url('inventory/products/' . $product_id));
            } else {
                flash('product_error', 'حدث خطأ أثناء تحديث المنتج', 'danger');
                redirect(base_url('inventory/products/' . $product_id . '/edit'));
            }

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * حذف منتج
     */
    public function delete()
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$product_id) {
                redirect(base_url('inventory/products'));
            }

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // حذف المنتج
            if ($this->productModel->delete($product_id, $company_id)) {
                flash('product_success', 'تم حذف المنتج بنجاح', 'success');
            } else {
                flash('product_error', 'حدث خطأ أثناء حذف المنتج', 'danger');
            }

            redirect(base_url('inventory/products'));

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * التحقق من صحة بيانات المنتج الديناميكية
     */
    private function validateDynamicProductData($data, $companyId)
    {
        $errors = [];

        // الحصول على الحقول المطلوبة للشركة
        $requiredFields = $this->fieldManager->getCompanyRequiredFields($companyId, 'inventory', 'products');

        // التحقق من الحقول المطلوبة
        foreach($requiredFields as $field) {
            $fieldName = $field['field_name'];
            $fieldLabel = $field['custom_label_ar'] ?: $field['field_label_ar'];

            if (empty($data[$fieldName])) {
                $errors[] = $fieldLabel . ' مطلوب';
            }
        }

        // التحقق من قواعد التحقق المخصصة
        $allFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');

        foreach($allFields as $field) {
            $fieldName = $field['field_name'];
            $fieldLabel = $field['custom_label_ar'] ?: $field['field_label_ar'];
            $value = $data[$fieldName] ?? '';

            // تطبيق قواعد التحقق
            if (!empty($field['custom_validation_rules'])) {
                $rules = json_decode($field['custom_validation_rules'], true);
                $this->applyValidationRules($value, $rules, $fieldLabel, $errors);
            } elseif (!empty($field['validation_rules'])) {
                $rules = json_decode($field['validation_rules'], true);
                $this->applyValidationRules($value, $rules, $fieldLabel, $errors);
            }
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }

    /**
     * تطبيق قواعد التحقق على حقل
     */
    private function applyValidationRules($value, $rules, $fieldLabel, &$errors)
    {
        if (!is_array($rules)) return;

        // التحقق من الحد الأدنى للطول
        if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
            $errors[] = $fieldLabel . ' يجب أن يكون على الأقل ' . $rules['min_length'] . ' أحرف';
        }

        // التحقق من الحد الأقصى للطول
        if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
            $errors[] = $fieldLabel . ' يجب أن يكون أقل من ' . $rules['max_length'] . ' حرف';
        }

        // التحقق من الحد الأدنى للقيمة
        if (isset($rules['min']) && is_numeric($value) && (float)$value < $rules['min']) {
            $errors[] = $fieldLabel . ' يجب أن يكون على الأقل ' . $rules['min'];
        }

        // التحقق من الحد الأقصى للقيمة
        if (isset($rules['max']) && is_numeric($value) && (float)$value > $rules['max']) {
            $errors[] = $fieldLabel . ' يجب أن يكون أقل من ' . $rules['max'];
        }

        // التحقق من النمط (Pattern)
        if (isset($rules['pattern']) && !empty($value) && !preg_match($rules['pattern'], $value)) {
            $errors[] = $fieldLabel . ' تنسيق غير صحيح';
        }
    }

    /**
     * التحقق من صحة بيانات المنتج (الطريقة القديمة - للتوافق)
     */
    private function validateProductData($data)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (empty($data['product_code'])) {
            $errors[] = 'كود المنتج مطلوب';
        }

        if (empty($data['product_name_ar'])) {
            $errors[] = 'اسم المنتج باللغة العربية مطلوب';
        }

        if (empty($data['category_id'])) {
            $errors[] = 'فئة المنتج مطلوبة';
        }

        if (empty($data['unit_id'])) {
            $errors[] = 'وحدة القياس مطلوبة';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }

        // إعداد البيانات
        return [
            'product_code' => trim($data['product_code']),
            'barcode' => trim($data['barcode'] ?? ''),
            'product_name_ar' => trim($data['product_name_ar']),
            'product_name_en' => trim($data['product_name_en'] ?? ''),
            'description_ar' => trim($data['description_ar'] ?? ''),
            'description_en' => trim($data['description_en'] ?? ''),
            'category_id' => (int)$data['category_id'],
            'unit_id' => (int)$data['unit_id'],
            'product_type' => $data['product_type'] ?? 'product',
            'track_inventory' => isset($data['track_inventory']) ? 1 : 0,
            'cost_price' => (float)($data['cost_price'] ?? 0),
            'selling_price' => (float)($data['selling_price'] ?? 0),
            'min_stock_level' => (float)($data['min_stock_level'] ?? 0),
            'max_stock_level' => !empty($data['max_stock_level']) ? (float)$data['max_stock_level'] : null,
            'reorder_point' => (float)($data['reorder_point'] ?? 0),
            'weight' => !empty($data['weight']) ? (float)$data['weight'] : null,
            'dimensions' => trim($data['dimensions'] ?? ''),
            'tax_rate' => (float)($data['tax_rate'] ?? 0),
            'is_active' => isset($data['is_active']) ? 1 : 0
        ];
    }
}
