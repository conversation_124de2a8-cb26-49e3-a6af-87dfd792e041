<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Models\DynamicProduct;
use App\Modules\Inventory\Models\Category;
use App\Modules\Inventory\Models\Unit;
use App\Core\DynamicTableManager;

/**
 * كنترولر المنتجات الديناميكي
 * يعمل مع الجداول المخصصة لكل شركة
 */
class DynamicProductController
{
    private $dynamicProduct;
    private $dynamicManager;
    private $companyId;
    
    public function __construct()
    {
        if (!is_logged_in()) {
            redirect(base_url('login'));
            return;
        }
        
        $user = current_user();
        $this->companyId = $user['current_company_id'];
        
        global $db;
        $this->dynamicManager = new DynamicTableManager($db);
        $this->dynamicProduct = new DynamicProduct($this->companyId);
    }
    
    /**
     * عرض قائمة المنتجات
     */
    public function index()
    {
        try {
            // التحقق من وجود جدول للشركة
            $hasTable = $this->dynamicManager->hasCompanyTable($this->companyId, 'inventory', 'product');
            
            if (!$hasTable) {
                view('Inventory::products/setup_required', [
                    'title' => 'إعداد المنتجات مطلوب',
                    'message' => 'يجب إعداد حقول المنتجات أولاً قبل البدء في استخدام النظام',
                    'setup_url' => base_url('inventory/settings/fields')
                ]);
                return;
            }
            
            // الحصول على المنتجات
            $products = $this->dynamicProduct->getAll();
            
            // الحصول على الحقول المرئية للعرض
            $visibleFields = $this->dynamicProduct->getVisibleFields();
            
            // تجميع الحقول حسب المجموعة للعرض المنظم
            $fieldGroups = $this->groupFieldsByCategory($visibleFields);
            
            view('Inventory::products/dynamic_index', [
                'title' => 'إدارة المنتجات',
                'products' => $products,
                'visibleFields' => $visibleFields,
                'fieldGroups' => $fieldGroups,
                'hasTable' => $hasTable
            ]);
            
        } catch (Exception $e) {
            view('Inventory::products/error', [
                'title' => 'خطأ في تحميل المنتجات',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * عرض نموذج إضافة منتج جديد
     */
    public function create()
    {
        try {
            // التحقق من وجود جدول للشركة
            $hasTable = $this->dynamicManager->hasCompanyTable($this->companyId, 'inventory', 'product');
            
            if (!$hasTable) {
                redirect(base_url('inventory/settings/fields?message=setup_required'));
                return;
            }
            
            // الحصول على الحقول المرئية
            $visibleFields = $this->dynamicProduct->getVisibleFields();
            $requiredFields = $this->dynamicProduct->getRequiredFields();
            
            // تجميع الحقول حسب المجموعة
            $fieldGroups = $this->groupFieldsByCategory($visibleFields);
            
            // الحصول على البيانات المساعدة
            $categories = [];
            $units = [];
            
            if ($this->hasField($visibleFields, 'category_id')) {
                $categoryModel = new Category();
                $categories = $categoryModel->getByCompany($this->companyId);
            }
            
            if ($this->hasField($visibleFields, 'unit_id')) {
                $unitModel = new Unit();
                $units = $unitModel->getByCompany($this->companyId);
            }
            
            view('Inventory::products/dynamic_create', [
                'title' => 'إضافة منتج جديد',
                'visibleFields' => $visibleFields,
                'requiredFields' => array_column($requiredFields, 'field_code'),
                'fieldGroups' => $fieldGroups,
                'categories' => $categories,
                'units' => $units
            ]);
            
        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }
    
    /**
     * حفظ منتج جديد
     */
    public function store()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/products/create'));
                return;
            }
            
            // التحقق من البيانات المطلوبة
            $this->validateRequiredFields($_POST);
            
            // إنشاء المنتج
            $productId = $this->dynamicProduct->create($_POST);
            
            if ($productId) {
                flash('product_success', 'تم إضافة المنتج بنجاح', 'success');
                
                // التحقق من وجود زر "حفظ وإضافة آخر"
                if (isset($_POST['save_and_new'])) {
                    redirect(base_url('inventory/products/create'));
                } else {
                    redirect(base_url('inventory/products'));
                }
            } else {
                flash('product_error', 'حدث خطأ أثناء إضافة المنتج', 'danger');
                redirect(base_url('inventory/products/create'));
            }
            
        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products/create'));
        }
    }
    
    /**
     * عرض تفاصيل منتج
     */
    public function show($id)
    {
        try {
            $product = $this->dynamicProduct->getById($id);
            
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
                return;
            }
            
            // الحصول على الحقول المرئية
            $visibleFields = $this->dynamicProduct->getVisibleFields();
            $fieldGroups = $this->groupFieldsByCategory($visibleFields);
            
            view('Inventory::products/dynamic_show', [
                'title' => $product['product_name_ar'],
                'product' => $product,
                'visibleFields' => $visibleFields,
                'fieldGroups' => $fieldGroups
            ]);
            
        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }
    
    /**
     * عرض نموذج تعديل منتج
     */
    public function edit($id)
    {
        try {
            $product = $this->dynamicProduct->getById($id);
            
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
                return;
            }
            
            // الحصول على الحقول المرئية
            $visibleFields = $this->dynamicProduct->getVisibleFields();
            $requiredFields = $this->dynamicProduct->getRequiredFields();
            $fieldGroups = $this->groupFieldsByCategory($visibleFields);
            
            // الحصول على البيانات المساعدة
            $categories = [];
            $units = [];
            
            if ($this->hasField($visibleFields, 'category_id')) {
                $categoryModel = new Category();
                $categories = $categoryModel->getByCompany($this->companyId);
            }
            
            if ($this->hasField($visibleFields, 'unit_id')) {
                $unitModel = new Unit();
                $units = $unitModel->getByCompany($this->companyId);
            }
            
            view('Inventory::products/dynamic_edit', [
                'title' => 'تعديل المنتج - ' . $product['product_name_ar'],
                'product' => $product,
                'visibleFields' => $visibleFields,
                'requiredFields' => array_column($requiredFields, 'field_code'),
                'fieldGroups' => $fieldGroups,
                'categories' => $categories,
                'units' => $units
            ]);
            
        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }
    
    /**
     * تحديث منتج
     */
    public function update($id)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/products'));
                return;
            }
            
            // التحقق من وجود المنتج
            $product = $this->dynamicProduct->getById($id);
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
                return;
            }
            
            // التحقق من البيانات المطلوبة
            $this->validateRequiredFields($_POST);
            
            // تحديث المنتج
            if ($this->dynamicProduct->update($id, $_POST)) {
                flash('product_success', 'تم تحديث المنتج بنجاح', 'success');
                redirect(base_url('inventory/products/' . $id));
            } else {
                flash('product_error', 'حدث خطأ أثناء تحديث المنتج', 'danger');
                redirect(base_url('inventory/products/' . $id . '/edit'));
            }
            
        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }
    
    /**
     * حذف منتج
     */
    public function delete($id)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/products'));
                return;
            }
            
            if ($this->dynamicProduct->delete($id)) {
                echo json_encode(['success' => true, 'message' => 'تم حذف المنتج بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف المنتج']);
            }
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * التحقق من البيانات المطلوبة
     */
    private function validateRequiredFields($data)
    {
        $requiredFields = $this->dynamicProduct->getRequiredFields();
        $errors = [];
        
        foreach ($requiredFields as $field) {
            $fieldCode = $field['field_code'];
            if (empty($data[$fieldCode])) {
                $errors[] = $field['field_name_ar'] . ' مطلوب';
            }
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }
    
    /**
     * تجميع الحقول حسب المجموعة
     */
    private function groupFieldsByCategory($fields)
    {
        $groups = [];
        foreach ($fields as $field) {
            $category = $field['category'] ?? 'general';
            $groups[$category][] = $field;
        }
        return $groups;
    }
    
    /**
     * التحقق من وجود حقل
     */
    private function hasField($fields, $fieldCode)
    {
        foreach ($fields as $field) {
            if ($field['field_code'] == $fieldCode) {
                return true;
            }
        }
        return false;
    }
}
