<?php
/**
 * اختبار النظام بعد الإصلاح
 */

// تضمين ملفات النظام
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/App/Core/FieldManager.php';
require_once __DIR__ . '/App/Modules/Inventory/Inventory/Models/Product.php';
require_once __DIR__ . '/App/Helpers/functions.php';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h1>🧪 اختبار النظام بعد الإصلاح</h1>";
    echo "<hr>";

    // معرف الشركة والمستخدم
    $companyId = 4;
    $userId = 32;

    // إنشاء النماذج
    $fieldManager = new App\Core\FieldManager($pdo);
    $productModel = new App\Modules\Inventory\Inventory\Models\Product($pdo, $fieldManager);

    echo "<h3>1️⃣ فحص إعدادات النظام الديناميكي:</h3>";
    
    // فحص إعدادات الشركة
    $hasSettings = $fieldManager->hasCompanyFieldSettings($companyId, 'inventory', 'products');
    echo "<p><strong>إعدادات الشركة:</strong> " . ($hasSettings ? "✅ موجودة" : "❌ غير موجودة") . "</p>";

    if (!$hasSettings) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ مشكلة في الإعدادات</h4>";
        echo "<p>يجب تشغيل dynamic_system_tables.sql أولاً</p>";
        echo "</div>";
        exit;
    }

    // فحص التبويبات
    $tabs = $fieldManager->getCompanyFieldsGroupedByTabs($companyId, 'inventory', 'products', 'form');
    echo "<p><strong>عدد التبويبات:</strong> " . count($tabs) . "</p>";

    if (empty($tabs)) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ لا توجد تبويبات</h4>";
        echo "<p>مشكلة في دالة getCompanyFieldsGroupedByTabs</p>";
        echo "</div>";
        exit;
    }

    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ النظام الديناميكي جاهز</h4>";
    echo "<ul>";
    foreach ($tabs as $tab) {
        echo "<li><strong>{$tab['tab_label_ar']}</strong> - " . count($tab['fields']) . " حقل</li>";
    }
    echo "</ul>";
    echo "</div>";

    echo "<h3>2️⃣ اختبار إنشاء منتج جديد:</h3>";

    // بيانات المنتج التجريبي
    $testData = [
        'product_code' => 'AUTO_' . time(),
        'product_name_ar' => 'منتج تلقائي ' . date('H:i:s'),
        'product_name_en' => 'Auto Product ' . date('H:i:s'),
        'category_id' => 1,
        'unit_id' => 1,
        'cost_price' => 50.00,
        'selling_price' => 75.00,
        'description_ar' => 'منتج تم إنشاؤه تلقائياً للاختبار',
        'barcode' => 'AUTO' . time(),
        'is_active' => 1
    ];

    echo "<p><strong>بيانات المنتج:</strong></p>";
    echo "<ul>";
    echo "<li>كود المنتج: {$testData['product_code']}</li>";
    echo "<li>اسم المنتج: {$testData['product_name_ar']}</li>";
    echo "<li>سعر التكلفة: {$testData['cost_price']} ريال</li>";
    echo "<li>سعر البيع: {$testData['selling_price']} ريال</li>";
    echo "</ul>";

    try {
        $productId = $productModel->createDynamicProduct($companyId, $testData, $userId);
        
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ نجح إنشاء المنتج!</h4>";
        echo "<p><strong>معرف المنتج الجديد:</strong> $productId</p>";
        echo "</div>";

        // التحقق من البيانات المحفوظة
        echo "<h3>3️⃣ التحقق من البيانات المحفوظة:</h3>";
        
        // فحص الجدول الأساسي
        $stmt = $pdo->prepare("SELECT product_code, product_name_ar, cost_price, selling_price FROM inventory_products WHERE product_id = ?");
        $stmt->execute([$productId]);
        $baseProduct = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($baseProduct) {
            echo "<p>✅ <strong>الجدول الأساسي:</strong></p>";
            echo "<ul>";
            echo "<li>كود المنتج: {$baseProduct['product_code']}</li>";
            echo "<li>اسم المنتج: {$baseProduct['product_name_ar']}</li>";
            echo "<li>سعر التكلفة: {$baseProduct['cost_price']}</li>";
            echo "<li>سعر البيع: {$baseProduct['selling_price']}</li>";
            echo "</ul>";
        }

        // فحص الجدول الديناميكي
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM dynamic_field_values 
            WHERE company_id = ? AND record_id = ?
        ");
        $stmt->execute([$companyId, $productId]);
        $dynamicCount = $stmt->fetchColumn();
        
        echo "<p>✅ <strong>الجدول الديناميكي:</strong> $dynamicCount قيمة محفوظة</p>";

        // اختبار استرجاع المنتج
        $retrievedProduct = $productModel->getDynamicProduct($productId, $companyId);
        echo "<p>✅ <strong>استرجاع المنتج:</strong> " . (count($retrievedProduct) > 0 ? "نجح" : "فشل") . "</p>";

    } catch (Exception $e) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ فشل في إنشاء المنتج!</h4>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
        echo "</div>";
    }

    echo "<h3>4️⃣ إحصائيات عامة:</h3>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_products WHERE company_id = ?");
    $stmt->execute([$companyId]);
    $totalProducts = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM dynamic_field_values WHERE company_id = ? AND module_name = 'inventory'");
    $stmt->execute([$companyId]);
    $totalDynamicValues = $stmt->fetchColumn();
    
    echo "<ul>";
    echo "<li><strong>إجمالي المنتجات:</strong> $totalProducts</li>";
    echo "<li><strong>إجمالي القيم الديناميكية:</strong> $totalDynamicValues</li>";
    echo "</ul>";

    echo "<h3>🔗 روابط الاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='/erpapp/inventory/products/create' target='_blank'>إضافة منتج جديد</a></li>";
    echo "<li><a href='/erpapp/inventory/products' target='_blank'>قائمة المنتجات</a></li>";
    echo "<li><a href='/erpapp/test_product_creation.php' target='_blank'>اختبار إنشاء منتج</a></li>";
    echo "</ul>";

    echo "<hr>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎉 النتيجة النهائية</h3>";
    echo "<p><strong>النظام يعمل بشكل صحيح!</strong></p>";
    echo "<p>يمكنك الآن استخدام نموذج إضافة المنتجات بدون مشاكل.</p>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<h2>❌ خطأ في قاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
