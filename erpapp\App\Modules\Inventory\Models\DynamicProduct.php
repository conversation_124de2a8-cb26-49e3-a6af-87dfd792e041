<?php
namespace App\Modules\Inventory\Models;

use App\Core\DynamicTableManager;
use PDO;
use Exception;

/**
 * نموذج المنتجات الديناميكي
 * يعمل مع الجداول المخصصة لكل شركة
 */
class DynamicProduct
{
    private $db;
    private $dynamicManager;
    private $companyId;
    private $tableName;
    private $enabledFields;
    
    public function __construct($companyId = null)
    {
        global $db;
        $this->db = $db;
        $this->dynamicManager = new DynamicTableManager($db);
        
        // تحديد الشركة
        $this->companyId = $companyId ?: current_user()['current_company_id'];
        
        // تحديد اسم الجدول
        $this->tableName = $this->dynamicManager->getCompanyTableName($this->companyId, 'inventory', 'product');
        
        // الحصول على الحقول المفعلة
        $this->enabledFields = $this->dynamicManager->getCompanyEnabledFields($this->companyId, 'inventory', 'product');
    }
    
    /**
     * الحصول على جميع المنتجات للشركة
     */
    public function getAll($filters = [])
    {
        // بناء الاستعلام الديناميكي
        $selectFields = $this->buildSelectFields();
        $joinClauses = $this->buildJoinClauses();
        $whereClause = "WHERE p.company_id = ?";
        $params = [$this->companyId];
        
        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $searchFields = $this->getSearchableFields();
            if (!empty($searchFields)) {
                $searchConditions = [];
                foreach ($searchFields as $field) {
                    $searchConditions[] = "p.{$field['field_code']} LIKE ?";
                    $params[] = '%' . $filters['search'] . '%';
                }
                $whereClause .= " AND (" . implode(' OR ', $searchConditions) . ")";
            }
        }
        
        if (!empty($filters['category_id'])) {
            $whereClause .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (isset($filters['is_active'])) {
            $whereClause .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }
        
        $sql = "SELECT {$selectFields} FROM {$this->tableName} p {$joinClauses} {$whereClause} ORDER BY p.product_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على منتج بالمعرف
     */
    public function getById($productId)
    {
        $selectFields = $this->buildSelectFields();
        $joinClauses = $this->buildJoinClauses();
        
        $sql = "SELECT {$selectFields} FROM {$this->tableName} p {$joinClauses} 
                WHERE p.product_id = ? AND p.company_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$productId, $this->companyId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * إنشاء منتج جديد
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['product_code'])) {
            throw new Exception('كود المنتج موجود مسبقاً');
        }
        
        // بناء الاستعلام الديناميكي
        $fields = [];
        $placeholders = [];
        $values = [];
        
        // إضافة الحقول الأساسية
        $fields[] = 'company_id';
        $placeholders[] = '?';
        $values[] = $this->companyId;
        
        // إضافة الحقول المفعلة
        foreach ($this->enabledFields as $field) {
            $fieldCode = $field['field_code'];
            
            // تخطي الحقول المخفية والمعرفات التلقائية
            if ($field['input_type'] == 'hidden' && $fieldCode != 'is_active') {
                continue;
            }
            
            if (isset($data[$fieldCode])) {
                $fields[] = $fieldCode;
                $placeholders[] = '?';
                $values[] = $this->formatFieldValue($data[$fieldCode], $field);
            } elseif ($field['default_value'] !== null) {
                $fields[] = $fieldCode;
                $placeholders[] = '?';
                $values[] = $field['default_value'];
            }
        }
        
        // إضافة حقول النظام
        $fields[] = 'created_by';
        $placeholders[] = '?';
        $values[] = current_user()['UserID'];
        
        $fields[] = 'created_at';
        $placeholders[] = 'NOW()';
        
        $sql = "INSERT INTO {$this->tableName} (" . implode(', ', $fields) . ") 
                VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute($values);
        
        return $result ? $this->db->lastInsertId() : false;
    }
    
    /**
     * تحديث منتج
     */
    public function update($productId, $data)
    {
        // التحقق من عدم تكرار الكود
        if (isset($data['product_code'])) {
            $existing = $this->getByCode($data['product_code']);
            if ($existing && $existing['product_id'] != $productId) {
                throw new Exception('كود المنتج موجود مسبقاً');
            }
        }
        
        // بناء الاستعلام الديناميكي
        $setFields = [];
        $values = [];
        
        foreach ($this->enabledFields as $field) {
            $fieldCode = $field['field_code'];
            
            // تخطي الحقول المخفية والمعرفات
            if ($field['input_type'] == 'hidden' && !in_array($fieldCode, ['is_active'])) {
                continue;
            }
            
            if (isset($data[$fieldCode])) {
                $setFields[] = "{$fieldCode} = ?";
                $values[] = $this->formatFieldValue($data[$fieldCode], $field);
            }
        }
        
        // إضافة حقول النظام
        $setFields[] = "updated_by = ?";
        $values[] = current_user()['UserID'];
        
        $setFields[] = "updated_at = NOW()";
        
        // إضافة شروط WHERE
        $values[] = $productId;
        $values[] = $this->companyId;
        
        $sql = "UPDATE {$this->tableName} SET " . implode(', ', $setFields) . " 
                WHERE product_id = ? AND company_id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    /**
     * حذف منتج
     */
    public function delete($productId)
    {
        // التحقق من عدم وجود حركات للمنتج
        $sql = "SELECT COUNT(*) FROM inventory_movements WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$productId, $this->companyId]);
        
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
        }
        
        $sql = "DELETE FROM {$this->tableName} WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$productId, $this->companyId]);
    }
    
    /**
     * الحصول على منتج بالكود
     */
    public function getByCode($productCode)
    {
        $sql = "SELECT product_id, product_code FROM {$this->tableName} 
                WHERE product_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$productCode, $this->companyId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على الحقول المفعلة
     */
    public function getEnabledFields()
    {
        return $this->enabledFields;
    }
    
    /**
     * الحصول على الحقول المرئية
     */
    public function getVisibleFields()
    {
        return array_filter($this->enabledFields, function($field) {
            return $field['is_visible'] == 1;
        });
    }
    
    /**
     * الحصول على الحقول القابلة للبحث
     */
    public function getSearchableFields()
    {
        return array_filter($this->enabledFields, function($field) {
            return $field['is_searchable'] == 1;
        });
    }
    
    /**
     * الحصول على الحقول المطلوبة
     */
    public function getRequiredFields()
    {
        return array_filter($this->enabledFields, function($field) {
            return $field['is_required'] == 1;
        });
    }
    
    /**
     * بناء حقول SELECT
     */
    private function buildSelectFields()
    {
        $fields = ['p.*'];
        
        // إضافة حقول الجداول المرتبطة
        if ($this->hasField('category_id')) {
            $fields[] = 'c.category_name_ar';
            $fields[] = 'c.category_name_en';
        }
        
        if ($this->hasField('unit_id')) {
            $fields[] = 'u.unit_name_ar';
            $fields[] = 'u.unit_name_en';
            $fields[] = 'u.unit_symbol_ar';
            $fields[] = 'u.unit_symbol_en';
        }
        
        return implode(', ', $fields);
    }
    
    /**
     * بناء JOIN clauses
     */
    private function buildJoinClauses()
    {
        $joins = [];
        
        if ($this->hasField('category_id')) {
            $joins[] = 'LEFT JOIN inventory_categories c ON p.category_id = c.category_id';
        }
        
        if ($this->hasField('unit_id')) {
            $joins[] = 'LEFT JOIN inventory_units u ON p.unit_id = u.unit_id';
        }
        
        return implode(' ', $joins);
    }
    
    /**
     * التحقق من وجود حقل
     */
    private function hasField($fieldCode)
    {
        foreach ($this->enabledFields as $field) {
            if ($field['field_code'] == $fieldCode) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * تنسيق قيمة الحقل
     */
    private function formatFieldValue($value, $field)
    {
        switch ($field['field_type']) {
            case 'TINYINT(1)':
                return $value ? 1 : 0;
            case 'INT':
                return (int)$value;
            case 'DECIMAL(15,2)':
            case 'DECIMAL(10,3)':
            case 'DECIMAL(5,2)':
                return (float)$value;
            case 'DATE':
                return $value ?: null;
            default:
                return $value ?: null;
        }
    }
}
