<?php
namespace App\Modules\Inventory\Services;

use App\Modules\Inventory\Models\Product;
use PDO;
use Exception;

/**
 * Inventory Service - خدمة المخزون
 */
class InventoryService
{
    /**
     * Product model
     */
    protected $productModel;

    /**
     * Category model
     */
    protected $categoryModel;

    /**
     * Warehouse model
     */
    protected $warehouseModel;

    /**
     * Stock model
     */
    protected $stockModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->productModel = new Product();
        // سيتم إنشاء باقي النماذج لاحقاً
    }

    /**
     * الحصول على إحصائيات لوحة التحكم
     */
    public function getDashboardStats()
    {
        // الحصول على معرف الشركة الحالية من المستخدم المسجل
        $user = current_user();
        $company_id = $user['current_company_id'] ?? null;

        // التحقق من وجود شركة حالية
        if (!$company_id) {
            return [
                'total_products' => 0,
                'active_products' => 0,
                'low_stock_products' => 0,
                'out_of_stock_products' => 0,
                'reorder_products' => 0,
                'total_inventory_value' => 0,
                'today_movements' => 0,
                'error' => 'لم يتم تحديد شركة حالية للمستخدم'
            ];
        }

        $stats = [
            // إحصائيات المنتجات
            'total_products' => 0,
            'active_products' => 0,
            'low_stock_count' => 0,
            'out_of_stock_count' => 0,
            'reorder_count' => 0,

            // إحصائيات الفئات
            'total_categories' => 0,
            'active_categories' => 0,

            // إحصائيات المخازن
            'total_warehouses' => 0,
            'active_warehouses' => 0,

            // إحصائيات القيم
            'total_inventory_value' => 0,
            'total_cost_value' => 0,

            // حركات اليوم
            'today_movements' => 0,
            'today_in_movements' => 0,
            'today_out_movements' => 0
        ];

        try {
            // إحصائيات المنتجات من النظام الديناميكي
            $stats['total_products'] = $this->getTotalProductsDynamic($company_id);
            $stats['active_products'] = $this->getTotalProductsDynamic($company_id);
            $stats['low_stock_count'] = $this->getReorderCount($company_id);
            $stats['out_of_stock_count'] = $this->getOutOfStockCountDynamic($company_id);

            // إحصائيات إضافية
            $stats['reorder_count'] = $this->getReorderCount($company_id);
            $stats['total_inventory_value'] = $this->getTotalInventoryValue($company_id);
            $stats['total_cost_value'] = $this->getTotalCostValue($company_id);

            // إحصائيات الحركات اليومية
            $movementStats = $this->getTodayMovementStats($company_id);
            $stats = array_merge($stats, $movementStats);

        } catch (Exception $e) {
            error_log('Error getting dashboard stats: ' . $e->getMessage());
        }

        return $stats;
    }

    /**
     * الحصول على عدد المنتجات التي تحتاج إعادة طلب
     * النظام الديناميكي الجديد
     */
    private function getReorderCount($company_id)
    {
        try {
            global $db;

            // جلب المنتجات التي تحتاج إعادة طلب من النظام الديناميكي
            $sql = "SELECT COUNT(DISTINCT dfv1.record_id)
                    FROM dynamic_field_values dfv1
                    JOIN system_fields sf1 ON dfv1.field_id = sf1.field_id AND sf1.field_name = 'current_stock'
                    JOIN dynamic_field_values dfv2 ON dfv1.record_id = dfv2.record_id
                        AND dfv1.company_id = dfv2.company_id
                    JOIN system_fields sf2 ON dfv2.field_id = sf2.field_id AND sf2.field_name = 'reorder_point'
                    JOIN dynamic_field_values dfv3 ON dfv1.record_id = dfv3.record_id
                        AND dfv1.company_id = dfv3.company_id
                    JOIN system_fields sf3 ON dfv3.field_id = sf3.field_id AND sf3.field_name = 'is_active'
                    WHERE dfv1.company_id = ?
                    AND dfv1.module_name = 'inventory'
                    AND dfv1.table_name = 'products'
                    AND dfv3.field_value = '1'
                    AND CAST(dfv1.field_value AS DECIMAL(10,2)) <= CAST(dfv2.field_value AS DECIMAL(10,2))";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log('Error getting reorder count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على إجمالي قيمة المخزون (بسعر البيع)
     * النظام الديناميكي الجديد
     */
    private function getTotalInventoryValue($company_id)
    {
        try {
            global $db;

            // حساب قيمة المخزون من النظام الديناميكي
            $sql = "SELECT SUM(
                        CAST(dfv_stock.field_value AS DECIMAL(10,2)) *
                        CAST(dfv_price.field_value AS DECIMAL(10,2))
                    ) as total_value
                    FROM dynamic_field_values dfv_stock
                    JOIN system_fields sf_stock ON dfv_stock.field_id = sf_stock.field_id
                        AND sf_stock.field_name = 'current_stock'
                    JOIN dynamic_field_values dfv_price ON dfv_stock.record_id = dfv_price.record_id
                        AND dfv_stock.company_id = dfv_price.company_id
                    JOIN system_fields sf_price ON dfv_price.field_id = sf_price.field_id
                        AND sf_price.field_name = 'selling_price'
                    JOIN dynamic_field_values dfv_active ON dfv_stock.record_id = dfv_active.record_id
                        AND dfv_stock.company_id = dfv_active.company_id
                    JOIN system_fields sf_active ON dfv_active.field_id = sf_active.field_id
                        AND sf_active.field_name = 'is_active'
                    WHERE dfv_stock.company_id = ?
                    AND dfv_stock.module_name = 'inventory'
                    AND dfv_stock.table_name = 'products'
                    AND dfv_active.field_value = '1'";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إجمالي قيمة التكلفة
     * النظام الديناميكي الجديد
     */
    private function getTotalCostValue($company_id)
    {
        try {
            global $db;

            // حساب قيمة التكلفة من النظام الديناميكي
            $sql = "SELECT SUM(
                        CAST(dfv_stock.field_value AS DECIMAL(10,2)) *
                        CAST(dfv_cost.field_value AS DECIMAL(10,2))
                    ) as total_cost
                    FROM dynamic_field_values dfv_stock
                    JOIN system_fields sf_stock ON dfv_stock.field_id = sf_stock.field_id
                        AND sf_stock.field_name = 'current_stock'
                    JOIN dynamic_field_values dfv_cost ON dfv_stock.record_id = dfv_cost.record_id
                        AND dfv_stock.company_id = dfv_cost.company_id
                    JOIN system_fields sf_cost ON dfv_cost.field_id = sf_cost.field_id
                        AND sf_cost.field_name = 'cost_price'
                    JOIN dynamic_field_values dfv_active ON dfv_stock.record_id = dfv_active.record_id
                        AND dfv_stock.company_id = dfv_active.company_id
                    JOIN system_fields sf_active ON dfv_active.field_id = sf_active.field_id
                        AND sf_active.field_name = 'is_active'
                    WHERE dfv_stock.company_id = ?
                    AND dfv_stock.module_name = 'inventory'
                    AND dfv_stock.table_name = 'products'
                    AND dfv_active.field_value = '1'";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إحصائيات حركات اليوم
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    private function getTodayMovementStats($company_id)
    {
        // إرجاع قيم افتراضية حتى يتم تطوير نظام الحركات الديناميكي
        return [
            'today_movements' => 0,
            'today_in_movements' => 0,
            'today_out_movements' => 0
        ];
    }

    /**
     * الحصول على المنتجات الأكثر حركة
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    public function getTopMovingProducts($company_id, $limit = 10)
    {
        // إرجاع مصفوفة فارغة حتى يتم تطوير نظام الحركات الديناميكي
        return [];
    }

    /**
     * الحصول على تقرير سريع للمخزون
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    public function getQuickStockReport($company_id)
    {
        // إرجاع مصفوفة فارغة حتى يتم تطوير نظام التقارير الديناميكي
        return [];
    }

    /**
     * التحقق من صحة المخزون
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    public function validateInventory($company_id)
    {
        // إرجاع مصفوفة فارغة حتى يتم تطوير نظام التحقق الديناميكي
        return [];
    }

    /**
     * الحصول على إجمالي عدد المنتجات من النظام الديناميكي
     */
    private function getTotalProductsDynamic($company_id)
    {
        try {
            global $db;

            $sql = "SELECT COUNT(DISTINCT record_id)
                    FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'products'";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log('Error getting total products count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على عدد المنتجات النافدة من النظام الديناميكي
     */
    private function getOutOfStockCountDynamic($company_id)
    {
        try {
            global $db;

            // جلب المنتجات النافدة من النظام الديناميكي
            $sql = "SELECT COUNT(DISTINCT dfv1.record_id)
                    FROM dynamic_field_values dfv1
                    JOIN system_fields sf1 ON dfv1.field_id = sf1.field_id AND sf1.field_name = 'current_stock'
                    JOIN dynamic_field_values dfv2 ON dfv1.record_id = dfv2.record_id
                        AND dfv1.company_id = dfv2.company_id
                    JOIN system_fields sf2 ON dfv2.field_id = sf2.field_id AND sf2.field_name = 'is_active'
                    WHERE dfv1.company_id = ?
                    AND dfv1.module_name = 'inventory'
                    AND dfv1.table_name = 'products'
                    AND dfv2.field_value = '1'
                    AND (dfv1.field_value = '0' OR dfv1.field_value = '' OR dfv1.field_value IS NULL)";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log('Error getting out of stock count: ' . $e->getMessage());
            return 0;
        }
    }
}
