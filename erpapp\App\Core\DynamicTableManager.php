<?php
namespace App\Core;

use PDO;
use Exception;

/**
 * مدير الجداول الديناميكية
 * يدير إنشاء وتعديل الجداول الديناميكية للشركات
 */
class DynamicTableManager
{
    private $db;
    
    public function __construct($database)
    {
        $this->db = $database;
    }
    
    /**
     * الحصول على الحقول المتاحة لوحدة معينة
     */
    public function getAvailableFields($moduleCode = 'inventory', $entityType = 'product')
    {
        $sql = "SELECT * FROM field_library 
                WHERE module_code = ? AND entity_type = ? 
                ORDER BY category, display_order";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$moduleCode, $entityType]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على الحقول المفعلة للشركة
     */
    public function getCompanyEnabledFields($companyId, $moduleCode = 'inventory', $entityType = 'product')
    {
        $sql = "SELECT fl.*, ces.is_enabled, ces.is_required, ces.is_visible, 
                       ces.is_searchable, ces.is_listable, ces.display_order as company_order,
                       ces.custom_label_ar, ces.custom_label_en, ces.field_group,
                       ces.validation_override
                FROM field_library fl
                JOIN company_entity_structure ces ON fl.field_id = ces.field_id
                WHERE ces.company_id = ? AND ces.module_code = ? 
                AND ces.entity_type = ? AND ces.is_enabled = 1
                ORDER BY ces.display_order, fl.display_order";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $moduleCode, $entityType]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * تفعيل/إلغاء تفعيل حقل للشركة
     */
    public function toggleFieldForCompany($companyId, $fieldId, $isEnabled, $userId, $options = [])
    {
        $sql = "INSERT INTO company_entity_structure 
                (company_id, module_code, entity_type, field_id, is_enabled, 
                 is_required, is_visible, is_searchable, is_listable, 
                 display_order, custom_label_ar, custom_label_en, field_group, 
                 validation_override, configured_by)
                SELECT ?, fl.module_code, fl.entity_type, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                FROM field_library fl WHERE fl.field_id = ?
                ON DUPLICATE KEY UPDATE 
                is_enabled = VALUES(is_enabled),
                is_required = VALUES(is_required),
                is_visible = VALUES(is_visible),
                is_searchable = VALUES(is_searchable),
                is_listable = VALUES(is_listable),
                display_order = VALUES(display_order),
                custom_label_ar = VALUES(custom_label_ar),
                custom_label_en = VALUES(custom_label_en),
                field_group = VALUES(field_group),
                validation_override = VALUES(validation_override),
                configured_by = VALUES(configured_by),
                configured_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $companyId, $fieldId, $isEnabled ? 1 : 0,
            $options['is_required'] ?? 0,
            $options['is_visible'] ?? 1,
            $options['is_searchable'] ?? 1,
            $options['is_listable'] ?? 1,
            $options['display_order'] ?? 0,
            $options['custom_label_ar'] ?? null,
            $options['custom_label_en'] ?? null,
            $options['field_group'] ?? 'general',
            $options['validation_override'] ? json_encode($options['validation_override']) : null,
            $userId,
            $fieldId
        ]);
    }
    
    /**
     * إنشاء جدول ديناميكي للشركة
     */
    public function createCompanyTable($companyId, $moduleCode = 'inventory', $entityType = 'product', $userId)
    {
        // التحقق من وجود حقول مفعلة
        $enabledFields = $this->getCompanyEnabledFields($companyId, $moduleCode, $entityType);
        
        if (empty($enabledFields)) {
            throw new Exception('لا توجد حقول مفعلة لإنشاء الجدول');
        }
        
        // التأكد من وجود الحقول الأساسية
        $coreFields = array_filter($enabledFields, function($field) {
            return $field['is_core_field'] == 1;
        });
        
        if (count($coreFields) < 5) { // على الأقل: id, company_id, product_code, product_name_ar, created_by
            throw new Exception('يجب تفعيل جميع الحقول الأساسية');
        }
        
        $tableName = "company_{$companyId}_{$moduleCode}_{$entityType}";
        
        // بناء SQL لإنشاء الجدول
        $createSQL = $this->buildCreateTableSQL($tableName, $enabledFields);
        
        try {
            // بدء المعاملة
            $this->db->beginTransaction();
            
            // حذف الجدول إذا كان موجوداً
            $this->db->exec("DROP TABLE IF EXISTS `{$tableName}`");
            
            // إنشاء الجدول الجديد
            $this->db->exec($createSQL);
            
            // تسجيل الجدول في السجل
            $this->registerTable($companyId, $moduleCode, $entityType, $tableName, $enabledFields, $userId);
            
            // تأكيد المعاملة
            $this->db->commit();
            
            return $tableName;
            
        } catch (Exception $e) {
            // إلغاء المعاملة في حالة الخطأ
            $this->db->rollBack();
            throw new Exception('فشل في إنشاء الجدول: ' . $e->getMessage());
        }
    }
    
    /**
     * بناء SQL لإنشاء الجدول
     */
    private function buildCreateTableSQL($tableName, $fields)
    {
        $sql = "CREATE TABLE `{$tableName}` (\n";
        $fieldDefinitions = [];
        $indexes = [];
        
        foreach ($fields as $field) {
            $fieldDefinitions[] = "  `{$field['field_code']}` {$field['mysql_type']}";
            
            // إضافة فهارس للحقول المهمة
            if ($field['field_code'] == 'product_code') {
                $indexes[] = "  UNIQUE KEY `idx_product_code` (`product_code`)";
            } elseif ($field['field_code'] == 'barcode') {
                $indexes[] = "  UNIQUE KEY `idx_barcode` (`barcode`)";
            } elseif (in_array($field['field_code'], ['category_id', 'unit_id', 'company_id'])) {
                $indexes[] = "  KEY `idx_{$field['field_code']}` (`{$field['field_code']}`)";
            }
        }
        
        $sql .= implode(",\n", $fieldDefinitions);
        
        if (!empty($indexes)) {
            $sql .= ",\n" . implode(",\n", $indexes);
        }
        
        $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        
        return $sql;
    }
    
    /**
     * تسجيل الجدول في السجل
     */
    private function registerTable($companyId, $moduleCode, $entityType, $tableName, $fields, $userId)
    {
        $tableStructure = [
            'table_name' => $tableName,
            'fields' => $fields,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO dynamic_tables_registry 
                (company_id, module_code, entity_type, table_name, table_structure, 
                 is_created, created_by)
                VALUES (?, ?, ?, ?, ?, 1, ?)
                ON DUPLICATE KEY UPDATE
                table_name = VALUES(table_name),
                table_structure = VALUES(table_structure),
                is_created = VALUES(is_created),
                last_modified_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $companyId, $moduleCode, $entityType, $tableName, 
            json_encode($tableStructure), $userId
        ]);
    }
    
    /**
     * التحقق من وجود جدول للشركة
     */
    public function hasCompanyTable($companyId, $moduleCode = 'inventory', $entityType = 'product')
    {
        $sql = "SELECT table_name FROM dynamic_tables_registry 
                WHERE company_id = ? AND module_code = ? AND entity_type = ? AND is_created = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $moduleCode, $entityType]);
        return $stmt->fetchColumn();
    }
    
    /**
     * الحصول على اسم جدول الشركة
     */
    public function getCompanyTableName($companyId, $moduleCode = 'inventory', $entityType = 'product')
    {
        $tableName = $this->hasCompanyTable($companyId, $moduleCode, $entityType);
        return $tableName ?: "company_{$companyId}_{$moduleCode}_{$entityType}";
    }
    
    /**
     * إنشاء إعدادات افتراضية للشركة الجديدة
     */
    public function createDefaultSettingsForCompany($companyId, $userId, $moduleCode = 'inventory', $entityType = 'product')
    {
        // الحصول على الحقول الأساسية
        $coreFields = $this->getAvailableFields($moduleCode, $entityType);
        $coreFields = array_filter($coreFields, function($field) {
            return $field['is_core_field'] == 1 || $field['is_required_default'] == 1;
        });
        
        // تفعيل الحقول الأساسية
        foreach ($coreFields as $field) {
            $this->toggleFieldForCompany($companyId, $field['field_id'], true, $userId, [
                'is_required' => $field['is_required_default'],
                'display_order' => $field['display_order']
            ]);
        }
        
        return count($coreFields);
    }
}
