<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;
use App\Core\FieldManager;

/**
 * Product Model - نموذج المنتجات
 */
class Product
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Field manager for dynamic fields
     */
    protected $fieldManager;

    /**
     * Table name
     */
    protected $table = 'inventory_products';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->fieldManager = new FieldManager($db);
    }

    /**
     * الحصول على جميع المنتجات للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?";

        $params = [$company_id];

        // تطبيق الفلاتر
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.product_name_ar LIKE ? OR p.product_name_en LIKE ? OR p.product_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        if (isset($filters['is_active'])) {
            $sql .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }

        $sql .= " GROUP BY p.product_id ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات مع تفاصيل كاملة للعرض في الجدول
     */
    public function getProductsWithDetails($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       c.category_name_en,
                       u.unit_name_ar,
                       u.unit_name_en,
                       u.unit_symbol_ar,
                       u.unit_symbol_en,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock,
                       COALESCE(SUM(s.quantity_available), 0) as available_stock,
                       COALESCE(SUM(s.quantity_reserved), 0) as reserved_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?
                GROUP BY p.product_id
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالمعرف
     */
    public function getById($product_id, $company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar, c.category_name_en,
                       u.unit_name_ar, u.unit_name_en, u.unit_symbol_ar, u.unit_symbol_en
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                WHERE p.product_id = ? AND p.company_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالكود
     */
    public function getByCode($product_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE product_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء منتج جديد
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['product_code'], $data['company_id'])) {
            throw new Exception('كود المنتج موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, product_code, barcode,
                    product_name_ar, product_name_en, description_ar, description_en,
                    category_id, unit_id, product_type, track_inventory,
                    cost_price, selling_price, min_stock_level, max_stock_level, reorder_point,
                    weight, dimensions, tax_rate, is_active, created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث منتج
     */
    public function update($product_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['product_code'])) {
            $existing = $this->getByCode($data['product_code'], $company_id);
            if ($existing && $existing['product_id'] != $product_id) {
                throw new Exception('كود المنتج موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    product_code = ?, barcode = ?,
                    product_name_ar = ?, product_name_en = ?,
                    description_ar = ?, description_en = ?,
                    category_id = ?, unit_id = ?, product_type = ?, track_inventory = ?,
                    cost_price = ?, selling_price = ?,
                    min_stock_level = ?, max_stock_level = ?, reorder_point = ?,
                    weight = ?, dimensions = ?, tax_rate = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE product_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['updated_by'],
            $product_id,
            $company_id
        ]);
    }

    /**
     * حذف منتج
     */
    public function delete($product_id, $company_id)
    {
        // التحقق من عدم وجود حركات للمنتج
        $sql = "SELECT COUNT(*) FROM inventory_movements WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
        }

        // حذف أرصدة المخزون أولاً
        $sql = "DELETE FROM inventory_stock WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        // حذف المنتج
        $sql = "DELETE FROM {$this->table} WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$product_id, $company_id]);
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock <= p.min_stock_level
                ORDER BY total_stock ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات النافدة
     */
    public function getOutOfStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock = 0
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على إحصائيات المنتجات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المنتجات
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_products'] = $stmt->fetchColumn();

        // المنتجات النشطة
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['active_products'] = $stmt->fetchColumn();

        // المنتجات منخفضة المخزون
        $stats['low_stock_count'] = count($this->getLowStockProducts($company_id));

        // المنتجات النافدة
        $stats['out_of_stock_count'] = count($this->getOutOfStockProducts($company_id));

        return $stats;
    }

    // ========================================
    // الدوال الديناميكية الجديدة
    // ========================================

    /**
     * الحصول على المنتجات مع القيم الديناميكية للعرض في الجداول
     */
    public function getDynamicProducts($companyId, $tableFields, $filters = [])
    {
        // بناء الاستعلام الأساسي
        $selectFields = ['p.product_id'];
        $joins = [];
        $fieldMap = [];

        // إضافة الحقول الديناميكية
        foreach($tableFields as $field) {
            $alias = "field_{$field['field_id']}";
            $selectFields[] = "{$alias}.field_value as {$field['field_name']}";
            $fieldMap[$field['field_name']] = $field;

            $joins[] = "LEFT JOIN dynamic_field_values {$alias}
                       ON p.product_id = {$alias}.record_id
                       AND {$alias}.field_id = {$field['field_id']}
                       AND {$alias}.company_id = {$companyId}
                       AND {$alias}.module_name = 'inventory'
                       AND {$alias}.table_name = 'products'";
        }

        $sql = "SELECT " . implode(', ', $selectFields) . "
                FROM {$this->table} p " . implode(' ', $joins) . "
                WHERE p.company_id = ?";

        $params = [$companyId];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $searchConditions = [];
            foreach($tableFields as $field) {
                if ($field['is_searchable']) {
                    $alias = "field_{$field['field_id']}";
                    $searchConditions[] = "{$alias}.field_value LIKE ?";
                    $params[] = '%' . $filters['search'] . '%';
                }
            }
            if (!empty($searchConditions)) {
                $sql .= " AND (" . implode(' OR ', $searchConditions) . ")";
            }
        }

        // تطبيق فلاتر الحقول
        foreach($filters as $key => $value) {
            if (strpos($key, 'filter_') === 0 && !empty($value)) {
                $fieldName = str_replace('filter_', '', $key);
                if (isset($fieldMap[$fieldName])) {
                    $alias = "field_{$fieldMap[$fieldName]['field_id']}";
                    $sql .= " AND {$alias}.field_value = ?";
                    $params[] = $value;
                }
            }
        }

        // ترتيب النتائج
        if (!empty($filters['sort']) && isset($fieldMap[$filters['sort']])) {
            $sortField = $fieldMap[$filters['sort']];
            $alias = "field_{$sortField['field_id']}";
            $direction = (!empty($filters['direction']) && $filters['direction'] == 'desc') ? 'DESC' : 'ASC';
            $sql .= " ORDER BY {$alias}.field_value {$direction}";
        } else {
            $sql .= " ORDER BY p.product_id DESC";
        }

        // تحديد عدد النتائج
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تنسيق النتائج
        foreach($results as &$row) {
            foreach($tableFields as $field) {
                if (isset($row[$field['field_name']])) {
                    $row[$field['field_name']] = $this->formatFieldValue($row[$field['field_name']], $field);
                }
            }
        }

        return $results;
    }

    /**
     * إنشاء منتج جديد مع النظام الديناميكي
     */
    public function createDynamicProduct($companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // إنشاء السجل الأساسي في جدول المنتجات
            $productId = $this->createBaseProduct($companyId, $userId, $data);

            // حفظ قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return $productId;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * تحديث منتج مع النظام الديناميكي
     */
    public function updateDynamicProduct($productId, $companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // تحديث قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على منتج مع قيمه الديناميكية
     */
    public function getDynamicProduct($productId, $companyId, $fields = null)
    {
        // الحصول على السجل الأساسي
        $product = ['product_id' => $productId];

        // الحصول على الحقول إذا لم يتم تمريرها
        if ($fields === null) {
            $fields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');
        }

        // الحصول على قيم الحقول الديناميكية
        $fieldValues = $this->fieldManager->getFieldValues($companyId, 'inventory', 'products', $productId);

        // دمج القيم مع معلومات الحقول
        foreach($fields as $field) {
            $fieldName = $field['field_name'];
            $value = $fieldValues[$fieldName] ?? $field['default_value'] ?? '';
            $product[$fieldName] = $this->formatFieldValue($value, $field);
        }

        return $product;
    }

    // ========================================
    // الدوال المساعدة للنظام الديناميكي
    // ========================================

    /**
     * إنشاء السجل الأساسي للمنتج
     */
    private function createBaseProduct($companyId, $userId, $data = [])
    {
        // التحقق من الحقول المطلوبة
        if (empty($data['product_code'])) {
            throw new Exception('كود المنتج مطلوب');
        }

        if (empty($data['product_name_ar'])) {
            throw new Exception('اسم المنتج مطلوب');
        }

        // استخراج الحقول الأساسية المطلوبة من البيانات
        $productCode = trim($data['product_code']);
        $productNameAr = trim($data['product_name_ar']);
        $productNameEn = !empty($data['product_name_en']) ? trim($data['product_name_en']) : null;
        $categoryId = !empty($data['category_id']) ? (int)$data['category_id'] : 1;
        $unitId = !empty($data['unit_id']) ? (int)$data['unit_id'] : 1;
        $costPrice = !empty($data['cost_price']) ? (float)$data['cost_price'] : 0.00;
        $sellingPrice = !empty($data['selling_price']) ? (float)$data['selling_price'] : 0.00;
        $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 1;

        // التحقق من عدم تكرار كود المنتج
        $checkSql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND product_code = ?";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->execute([$companyId, $productCode]);

        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception("كود المنتج '$productCode' موجود مسبقاً");
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, product_code, product_name_ar, product_name_en,
                    category_id, unit_id, cost_price, selling_price,
                    is_active, created_by, created_at
                ) VALUES (?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $companyId, $productCode, $productNameAr, $productNameEn,
            $categoryId, $unitId, $costPrice, $sellingPrice,
            $isActive, $userId
        ]);

        if (!$result) {
            throw new Exception('فشل في إنشاء المنتج: ' . implode(', ', $stmt->errorInfo()));
        }

        return $this->db->lastInsertId();
    }

    /**
     * حفظ قيم الحقول الديناميكية
     */
    private function saveDynamicFieldValues($companyId, $productId, $data, $userId)
    {
        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);

                $this->fieldManager->saveFieldValue(
                    $companyId,
                    'inventory',
                    'products',
                    $productId,
                    $field['field_id'],
                    $value,
                    $userId
                );
            }
        }
    }

    /**
     * معالجة قيمة الحقل قبل الحفظ
     */
    private function processFieldValue($value, $field)
    {
        switch($field['field_type']) {
            case 'INT':
                return (int)$value;

            case 'DECIMAL':
                return (float)$value;

            case 'TINYINT':
                return $value ? 1 : 0;

            case 'DATE':
                return !empty($value) ? date('Y-m-d', strtotime($value)) : null;

            case 'TIMESTAMP':
                return !empty($value) ? date('Y-m-d H:i:s', strtotime($value)) : null;

            default:
                return trim($value);
        }
    }

    /**
     * تنسيق قيمة الحقل للعرض
     */
    private function formatFieldValue($value, $field)
    {
        if (empty($value)) {
            return '';
        }

        switch($field['field_type']) {
            case 'DECIMAL':
                $precision = 2;
                if (!empty($field['field_length'])) {
                    $parts = explode(',', $field['field_length']);
                    if (count($parts) == 2) {
                        $precision = (int)$parts[1];
                    }
                }
                return number_format((float)$value, $precision);

            case 'TINYINT':
                if ($field['input_type'] == 'checkbox') {
                    return $value ? 'نعم' : 'لا';
                }
                return $value;

            case 'DATE':
                return date('Y-m-d', strtotime($value));

            case 'TIMESTAMP':
                return date('Y-m-d H:i:s', strtotime($value));

            default:
                return htmlspecialchars($value);
        }
    }

    /**
     * حذف منتج مع قيمه الديناميكية
     */
    public function deleteDynamicProduct($productId, $companyId)
    {
        try {
            $this->db->beginTransaction();

            // حذف قيم الحقول الديناميكية
            $sql = "DELETE FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = 'inventory'
                    AND table_name = 'products' AND record_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$companyId, $productId]);

            // حذف السجل الأساسي
            $sql = "DELETE FROM {$this->table} WHERE product_id = ? AND company_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$productId, $companyId]);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * البحث في المنتجات الديناميكية
     */
    public function searchDynamicProducts($companyId, $searchTerm, $searchFields = null)
    {
        if ($searchFields === null) {
            $searchFields = $this->fieldManager->getCompanySearchableFields($companyId, 'inventory', 'products');
        }

        if (empty($searchFields)) {
            return [];
        }

        $selectFields = ['p.product_id'];
        $joins = [];
        $searchConditions = [];
        $params = [$companyId];

        foreach($searchFields as $field) {
            $alias = "field_{$field['field_id']}";
            $selectFields[] = "{$alias}.field_value as {$field['field_name']}";

            $joins[] = "LEFT JOIN dynamic_field_values {$alias}
                       ON p.product_id = {$alias}.record_id
                       AND {$alias}.field_id = {$field['field_id']}
                       AND {$alias}.company_id = {$companyId}";

            $searchConditions[] = "{$alias}.field_value LIKE ?";
            $params[] = '%' . $searchTerm . '%';
        }

        $sql = "SELECT " . implode(', ', $selectFields) . "
                FROM {$this->table} p " . implode(' ', $joins) . "
                WHERE p.company_id = ? AND (" . implode(' OR ', $searchConditions) . ")
                ORDER BY p.product_id DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
