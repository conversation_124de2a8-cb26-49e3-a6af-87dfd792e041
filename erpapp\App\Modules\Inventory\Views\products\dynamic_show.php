<?php
/**
 * نموذج تعديل منتج ديناميكي
 * يعرض النموذج حسب الحقول والتبويبات المختارة للشركة
 */
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-1">
                <i class="fas fa-eye text-info me-2"></i>
                <?= $title ?>
            </h2>
            <p class="text-muted mb-0">عرض تفاصيل المنتج بالنظام الديناميكي</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة للقائمة
            </a>
            <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i>
                عرض المنتج
            </a>
        </div>
    </div>

    <!-- تشخيص المشكلة -->
    <?php if(empty($tabs)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>لا توجد حقول مكونة</h5>
            <p>لم يتم العثور على حقول مكونة لهذه الشركة. يجب إعداد الحقول أولاً.</p>
            <a href="<?= base_url('inventory/settings/fields') ?>" class="btn btn-primary">
                <i class="fas fa-cog me-1"></i>إعداد الحقول الآن
            </a>
        </div>

        <!-- معلومات التشخيص -->
        <div class="card mt-3">
            <div class="card-header">معلومات التشخيص</div>
            <div class="card-body">
                <pre><?php
                echo "Company ID: " . ($company_id ?? 'غير محدد') . "\n";
                echo "Product ID: " . ($product['product_id'] ?? 'غير محدد') . "\n";
                echo "Tabs count: " . count($tabs ?? []) . "\n";
                echo "Categories count: " . count($categories ?? []) . "\n";
                echo "Units count: " . count($units ?? []) . "\n";
                echo "DetailFields count: " . count($detailFields ?? []) . "\n";
                echo "Product data keys: " . implode(', ', array_keys($product ?? [])) . "\n";
                if (!empty($tabs)) {
                    echo "First tab: " . print_r($tabs[0] ?? [], true) . "\n";
                }
                ?></pre>
            </div>
        </div>
    <?php else: ?>

    <!-- عرض تفاصيل المنتج -->

        <div class="row">
            <!-- التبويبات الديناميكية -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <!-- Nav Tabs -->
                        <ul class="nav nav-tabs" id="productTabs" role="tablist">
                            <?php $firstTab = true; ?>
                            <?php foreach($tabs as $tab): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?= $firstTab ? 'active' : '' ?>" 
                                            id="tab-<?= $tab['tab_id'] ?>-tab" 
                                            data-bs-toggle="tab" 
                                            data-bs-target="#tab-<?= $tab['tab_id'] ?>" 
                                            type="button" role="tab">
                                        <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                        <?= $tab['tab_label_ar'] ?>
                                        <span class="badge bg-secondary ms-2"><?= count($tab['fields']) ?></span>
                                    </button>
                                </li>
                                <?php $firstTab = false; ?>
                            <?php endforeach; ?>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content mt-4" id="productTabsContent">
                            <?php $firstTab = true; ?>
                            <?php foreach($tabs as $tab): ?>
                                <div class="tab-pane fade <?= $firstTab ? 'show active' : '' ?>" 
                                     id="tab-<?= $tab['tab_id'] ?>" role="tabpanel">
                                    
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <h5 class="text-muted">
                                                <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                                <?= $tab['tab_label_ar'] ?>
                                            </h5>
                                            <hr>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <?php foreach($tab['fields'] as $field): ?>
                                            <?php if($field['is_visible_form'] && !$field['is_hidden']): ?>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">
                                                        <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                                        <?php if($field['is_required']): ?>
                                                            <span class="text-danger">*</span>
                                                        <?php endif; ?>
                                                    </label>

                                                    <?php
                                                    $fieldName = $field['field_name'];
                                                    $helpText = $field['custom_help_text_ar'] ?: $field['help_text_ar'] ?: '';
                                                    $value = $product[$fieldName] ?? $field['default_value'] ?? '';
                                                    ?>

                                                    <!-- عرض القيمة بدلاً من حقل الإدخال -->
                                                    <div class="form-control-plaintext bg-light p-3 rounded border">
                                                        <?php if($field['input_type'] == 'text' || $field['input_type'] == 'number'): ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">غير محدد</span>
                                                            <?php elseif($field['field_name'] == 'product_code'): ?>
                                                                <code class="fs-6"><?= htmlspecialchars($value) ?></code>
                                                            <?php elseif($field['field_type'] == 'DECIMAL' && is_numeric($value)): ?>
                                                                <span class="badge bg-info fs-6"><?= number_format($value, 2) ?></span>
                                                            <?php else: ?>
                                                                <?= htmlspecialchars($value) ?>
                                                            <?php endif; ?>

                                                        <?php elseif($field['input_type'] == 'textarea'): ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">غير محدد</span>
                                                            <?php else: ?>
                                                                <div class="text-break"><?= nl2br(htmlspecialchars($value)) ?></div>
                                                            <?php endif; ?>

                                                        <?php elseif($field['input_type'] == 'select'): ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">غير محدد</span>
                                                            <?php else: ?>
                                                                <?php
                                                                // عرض النص بدلاً من القيمة للقوائم المنسدلة
                                                                if($fieldName == 'category_id' && !empty($categories)) {
                                                                    foreach($categories as $category) {
                                                                        if($value == $category['category_id']) {
                                                                            echo '<span class="badge bg-success">' . htmlspecialchars($category['category_name_ar']) . '</span>';
                                                                            break;
                                                                        }
                                                                    }
                                                                } elseif($fieldName == 'unit_id' && !empty($units)) {
                                                                    foreach($units as $unit) {
                                                                        if($value == $unit['unit_id']) {
                                                                            echo '<span class="badge bg-primary">' . htmlspecialchars($unit['unit_name_ar']) . '</span>';
                                                                            break;
                                                                        }
                                                                    }
                                                                } else {
                                                                    echo htmlspecialchars($value);
                                                                }
                                                                ?>
                                                            <?php endif; ?>

                                                        <?php elseif($field['input_type'] == 'checkbox'): ?>
                                                            <?= $value ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>' ?>

                                                        <?php elseif($field['input_type'] == 'file'): ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">لا يوجد ملف</span>
                                                            <?php else: ?>
                                                                <?php if(strpos($value, 'image') !== false): ?>
                                                                    <img src="<?= htmlspecialchars($value) ?>" class="img-thumbnail" style="max-width: 200px;">
                                                                <?php else: ?>
                                                                    <a href="<?= htmlspecialchars($value) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                        <i class="fas fa-download me-1"></i>تحميل الملف
                                                                    </a>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                        <?php elseif($field['input_type'] == 'date'): ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">غير محدد</span>
                                                            <?php else: ?>
                                                                <span class="text-info"><?= date('Y-m-d', strtotime($value)) ?></span>
                                                            <?php endif; ?>

                                                        <?php else: ?>
                                                            <?php if(empty($value)): ?>
                                                                <span class="text-muted">غير محدد</span>
                                                            <?php else: ?>
                                                                <?= htmlspecialchars($value) ?>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>

                                                    <?php if($helpText): ?>
                                                        <div class="form-text text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            <?= htmlspecialchars($helpText) ?>
                                                        </div>
                                                    <?php endif; ?>


                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php $firstTab = false; ?>
                            <?php endforeach; ?>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" class="btn btn-warning">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل المنتج
                                        </a>
                                        <button type="button" class="btn btn-outline-info" onclick="printProduct()">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-success" onclick="exportProduct()">
                                            <i class="fas fa-download me-1"></i>
                                            تصدير
                                        </button>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="deleteProduct(<?= $product['product_id'] ?>)">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التبويبات
    const tabTriggerList = [].slice.call(document.querySelectorAll('#productTabs button'));
    tabTriggerList.forEach(function (tabTriggerEl) {
        new bootstrap.Tab(tabTriggerEl);
    });
});

function printProduct() {
    window.print();
}

function exportProduct() {
    // تصدير بيانات المنتج
    const productData = <?= json_encode($product) ?>;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(productData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "product_<?= $product['product_id'] ?>.json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
}

function deleteProduct(productId) {
    if(confirm('هل أنت متأكد من حذف هذا المنتج؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('inventory/products/') ?>${productId}/delete`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= csrf_token() ?>';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    margin-bottom: -1px;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1.5rem;
    border-radius: 0 0 0.5rem 0.5rem;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control-plaintext {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    min-height: 38px;
    display: flex;
    align-items: center;
}

.img-thumbnail {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

code {
    background-color: #f1f3f4;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 1rem;
}

@media print {
    .btn, .nav-tabs {
        display: none !important;
    }

    .tab-content {
        border: none !important;
    }

    .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
}

.badge {
    font-size: 0.75rem;
}
</style>

<?php endif; ?>
