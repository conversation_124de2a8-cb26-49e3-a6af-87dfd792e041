<?php
/**
 * عرض تفاصيل المنتج الديناميكي
 * يعرض تفاصيل المنتج حسب الحقول المختارة للشركة
 */
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-1">
                <i class="fas fa-box text-primary me-2"></i>
                <?= $title ?>
            </h2>
            <p class="text-muted mb-0">عرض تفاصيل المنتج بالنظام الديناميكي</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" 
               class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- معلومات المنتج -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <!-- Nav Tabs -->
                    <ul class="nav nav-tabs" id="productDetailTabs" role="tablist">
                        <?php $firstTab = true; ?>
                        <?php foreach($tabs as $tab): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?= $firstTab ? 'active' : '' ?>" 
                                        id="tab-<?= $tab['tab_id'] ?>-tab" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#tab-<?= $tab['tab_id'] ?>" 
                                        type="button" role="tab">
                                    <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                    <?= $tab['tab_label_ar'] ?>
                                    <span class="badge bg-secondary ms-2"><?= count($tab['fields']) ?></span>
                                </button>
                            </li>
                            <?php $firstTab = false; ?>
                        <?php endforeach; ?>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-4" id="productDetailTabsContent">
                        <?php $firstTab = true; ?>
                        <?php foreach($tabs as $tab): ?>
                            <div class="tab-pane fade <?= $firstTab ? 'show active' : '' ?>" 
                                 id="tab-<?= $tab['tab_id'] ?>" role="tabpanel">
                                
                                <div class="row">
                                    <div class="col-md-12 mb-3">
                                        <h5 class="text-muted">
                                            <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                            <?= $tab['tab_label_ar'] ?>
                                        </h5>
                                        <hr>
                                    </div>
                                </div>

                                <div class="row">
                                    <?php foreach($tab['fields'] as $field): ?>
                                        <?php if($field['is_visible_details']): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="detail-field">
                                                    <label class="detail-label">
                                                        <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>:
                                                    </label>
                                                    <div class="detail-value">
                                                        <?php
                                                        $fieldName = $field['field_name'];
                                                        $value = $product[$fieldName] ?? '';
                                                        
                                                        // تنسيق القيم حسب نوع الحقل
                                                        if (empty($value)) {
                                                            echo '<span class="text-muted">غير محدد</span>';
                                                        } elseif ($field['field_type'] == 'DECIMAL' && is_numeric($value)) {
                                                            echo '<span class="badge bg-info fs-6">' . number_format($value, 2) . '</span>';
                                                        } elseif ($field['input_type'] == 'checkbox') {
                                                            echo $value ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>';
                                                        } elseif ($field['field_name'] == 'product_code') {
                                                            echo '<code class="fs-6">' . htmlspecialchars($value) . '</code>';
                                                        } elseif ($field['field_type'] == 'TEXT' || $field['input_type'] == 'textarea') {
                                                            echo '<div class="text-break">' . nl2br(htmlspecialchars($value)) . '</div>';
                                                        } elseif ($field['input_type'] == 'file' && !empty($value)) {
                                                            if (strpos($value, 'image') !== false) {
                                                                echo '<img src="' . htmlspecialchars($value) . '" class="img-thumbnail" style="max-width: 200px;">';
                                                            } else {
                                                                echo '<a href="' . htmlspecialchars($value) . '" target="_blank" class="btn btn-sm btn-outline-primary">';
                                                                echo '<i class="fas fa-download me-1"></i>تحميل الملف</a>';
                                                            }
                                                        } elseif ($field['field_type'] == 'DATE') {
                                                            echo '<span class="text-info">' . date('Y-m-d', strtotime($value)) . '</span>';
                                                        } elseif ($field['field_type'] == 'TIMESTAMP') {
                                                            echo '<span class="text-info">' . date('Y-m-d H:i:s', strtotime($value)) . '</span>';
                                                        } else {
                                                            echo '<span>' . htmlspecialchars($value) . '</span>';
                                                        }
                                                        ?>
                                                    </div>
                                                    
                                                    <?php if(!empty($field['help_text_ar'])): ?>
                                                        <small class="text-muted d-block mt-1">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            <?= htmlspecialchars($field['help_text_ar']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php $firstTab = false; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات وإجراءات إضافية -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات المنتج
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <h5 class="text-primary mb-1">
                                    <?= $product['current_stock'] ?? '0' ?>
                                </h5>
                                <small class="text-muted">المخزون الحالي</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h5 class="text-success mb-1">
                                    <?= $product['selling_price'] ?? '0.00' ?>
                                </h5>
                                <small class="text-muted">سعر البيع</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h5 class="text-info mb-1">
                                    <?= $product['cost_price'] ?? '0.00' ?>
                                </h5>
                                <small class="text-muted">سعر التكلفة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>
                            تعديل المنتج
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="printProduct()">
                            <i class="fas fa-print me-2"></i>
                            طباعة التفاصيل
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="exportProduct()">
                            <i class="fas fa-download me-2"></i>
                            تصدير البيانات
                        </button>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="deleteProduct(<?= $product['product_id'] ?>)">
                            <i class="fas fa-trash me-2"></i>
                            حذف المنتج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>تاريخ الإنشاء:</strong> 
                                <?= $product['created_at'] ?? 'غير محدد' ?>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <strong>آخر تحديث:</strong> 
                                <?= $product['updated_at'] ?? 'غير محدد' ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التبويبات
    const tabTriggerList = [].slice.call(document.querySelectorAll('#productDetailTabs button'));
    tabTriggerList.forEach(function (tabTriggerEl) {
        new bootstrap.Tab(tabTriggerEl);
    });
});

function printProduct() {
    window.print();
}

function exportProduct() {
    // تصدير بيانات المنتج
    const productData = <?= json_encode($product) ?>;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(productData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "product_<?= $product['product_id'] ?>.json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
}

function deleteProduct(productId) {
    if(confirm('هل أنت متأكد من حذف هذا المنتج؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('inventory/products/') ?>${productId}/delete`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= csrf_token() ?>';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
.detail-field {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #007bff;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.detail-value {
    font-size: 1rem;
    color: #212529;
}

.stat-item {
    padding: 0.5rem;
}

.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    margin-bottom: -1px;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1.5rem;
    border-radius: 0 0 0.5rem 0.5rem;
}

.badge {
    font-size: 0.875rem;
}

code {
    background-color: #f1f3f4;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 1rem;
}

.img-thumbnail {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

@media print {
    .btn, .nav-tabs {
        display: none !important;
    }
    
    .tab-content {
        border: none !important;
    }
    
    .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
}
</style>
