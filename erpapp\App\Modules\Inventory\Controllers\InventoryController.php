<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Services\InventoryService;
use Exception;
use PDO;

/**
 * Inventory Controller - المتحكم الرئيسي للمخزون
 */
class InventoryController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Inventory service
     */
    protected $inventoryService;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->inventoryService = new InventoryService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('inventory_error', 'يجب تحديد شركة حالية للوصول إلى وحدة المخزون', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض لوحة تحكم المخزون الرئيسية
     */
    public function index()
    {
        try {
            // الحصول على إحصائيات المخزون
            $stats = $this->inventoryService->getDashboardStats();

            // الحصول على معلومات الشركة الحالية
            $user = current_user();
            $company_id = $user['current_company_id'];

            // جلب معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(PDO::FETCH_ASSOC);

            $data = [
                'title' => 'وحدة إدارة المخزون',
                'stats' => $stats,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'active' => true]
                ]
            ];

            // استخدام view helper مثل ProductController
            view('Inventory::dashboard/index', $data);

        } catch (Exception $e) {
            flash('inventory_error', 'حدث خطأ أثناء تحميل لوحة تحكم المخزون: ' . $e->getMessage(), 'danger');
            redirect(base_url('dashboard'));
        }
    }

    /**
     * عرض لوحة تحكم المخزون
     */
    public function dashboard()
    {
        return $this->index();
    }
}
