<?php
/**
 * تعديل منتج - النظام الديناميكي الجديد
 * يعتمد على الحقول المختارة من قبل الشركة
 */

// التحقق من وجود البيانات المطلوبة
if (!isset($product) || !isset($tabs)) {
    echo '<div class="alert alert-danger">خطأ في تحميل بيانات المنتج</div>';
    return;
}
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit text-primary me-2"></i>
                تعديل المنتج
            </h1>
            <p class="text-muted mb-0">النظام الديناميكي - الحقول المخصصة</p>
        </div>
        <div>
            <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>
                عرض المنتج
            </a>
        </div>
    </div>

    <!-- Form -->
    <form method="POST" action="<?= base_url('inventory/products/' . $product['product_id'] . '/update') ?>" id="productForm">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-edit me-2"></i>
                            تعديل بيانات المنتج
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($tabs)): ?>
                            <!-- Navigation Tabs -->
                            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                                <?php $firstTab = true; ?>
                                <?php foreach($tabs as $tabKey => $tab): ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link <?= $firstTab ? 'active' : '' ?>" 
                                                id="<?= $tabKey ?>-tab" 
                                                data-bs-toggle="tab" 
                                                data-bs-target="#<?= $tabKey ?>" 
                                                type="button" 
                                                role="tab">
                                            <i class="<?= $tab['icon'] ?? 'fas fa-folder' ?> me-2"></i>
                                            <?= $tab['tab_label_ar'] ?>
                                        </button>
                                    </li>
                                    <?php $firstTab = false; ?>
                                <?php endforeach; ?>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content mt-3" id="productTabsContent">
                                <?php $firstTab = true; ?>
                                <?php foreach($tabs as $tabKey => $tab): ?>
                                    <div class="tab-pane fade <?= $firstTab ? 'show active' : '' ?>" 
                                         id="<?= $tabKey ?>" 
                                         role="tabpanel">
                                        
                                        <div class="row">
                                            <?php foreach($tab['fields'] as $field): ?>
                                                <div class="col-md-<?= $field['form_column_width'] ?? 6 ?> mb-3">
                                                    <label for="<?= $field['field_name'] ?>" class="form-label">
                                                        <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                                        <?php if (in_array($field['field_name'], $requiredFields ?? [])): ?>
                                                            <span class="text-danger">*</span>
                                                        <?php endif; ?>
                                                    </label>

                                                    <?php
                                                    $fieldValue = $product[$field['field_name']] ?? $field['default_value'] ?? '';
                                                    $fieldName = $field['field_name'];
                                                    $isRequired = in_array($field['field_name'], $requiredFields ?? []);
                                                    ?>

                                                    <?php if ($field['field_type'] == 'VARCHAR' || $field['field_type'] == 'TEXT'): ?>
                                                        <?php if ($field['field_name'] == 'category_id'): ?>
                                                            <select name="<?= $fieldName ?>" 
                                                                    id="<?= $fieldName ?>" 
                                                                    class="form-select <?= $isRequired ? 'required' : '' ?>">
                                                                <option value="">اختر الفئة</option>
                                                                <?php foreach($categories as $category): ?>
                                                                    <option value="<?= $category['category_id'] ?>" 
                                                                            <?= $fieldValue == $category['category_id'] ? 'selected' : '' ?>>
                                                                        <?= $category['category_name_ar'] ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        <?php elseif ($field['field_name'] == 'unit_id'): ?>
                                                            <select name="<?= $fieldName ?>" 
                                                                    id="<?= $fieldName ?>" 
                                                                    class="form-select <?= $isRequired ? 'required' : '' ?>">
                                                                <option value="">اختر الوحدة</option>
                                                                <?php foreach($units as $unit): ?>
                                                                    <option value="<?= $unit['unit_id'] ?>" 
                                                                            <?= $fieldValue == $unit['unit_id'] ? 'selected' : '' ?>>
                                                                        <?= $unit['unit_name_ar'] ?> (<?= $unit['unit_symbol_ar'] ?>)
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        <?php elseif ($field['field_type'] == 'TEXT'): ?>
                                                            <textarea name="<?= $fieldName ?>" 
                                                                      id="<?= $fieldName ?>" 
                                                                      class="form-control <?= $isRequired ? 'required' : '' ?>" 
                                                                      rows="3"
                                                                      placeholder="<?= $field['placeholder'] ?? '' ?>"><?= htmlspecialchars($fieldValue) ?></textarea>
                                                        <?php else: ?>
                                                            <input type="text" 
                                                                   name="<?= $fieldName ?>" 
                                                                   id="<?= $fieldName ?>" 
                                                                   class="form-control <?= $isRequired ? 'required' : '' ?>" 
                                                                   value="<?= htmlspecialchars($fieldValue) ?>"
                                                                   placeholder="<?= $field['placeholder'] ?? '' ?>">
                                                        <?php endif; ?>

                                                    <?php elseif ($field['field_type'] == 'INT' || $field['field_type'] == 'DECIMAL'): ?>
                                                        <input type="number" 
                                                               name="<?= $fieldName ?>" 
                                                               id="<?= $fieldName ?>" 
                                                               class="form-control <?= $isRequired ? 'required' : '' ?>" 
                                                               value="<?= $fieldValue ?>"
                                                               <?= $field['field_type'] == 'DECIMAL' ? 'step="0.01"' : '' ?>
                                                               placeholder="<?= $field['placeholder'] ?? '' ?>">

                                                    <?php elseif ($field['field_type'] == 'DATE'): ?>
                                                        <input type="date" 
                                                               name="<?= $fieldName ?>" 
                                                               id="<?= $fieldName ?>" 
                                                               class="form-control <?= $isRequired ? 'required' : '' ?>" 
                                                               value="<?= $fieldValue ?>">

                                                    <?php elseif ($field['field_type'] == 'TINYINT'): ?>
                                                        <div class="form-check form-switch">
                                                            <input type="hidden" name="<?= $fieldName ?>" value="0">
                                                            <input type="checkbox" 
                                                                   name="<?= $fieldName ?>" 
                                                                   id="<?= $fieldName ?>" 
                                                                   class="form-check-input" 
                                                                   value="1"
                                                                   <?= $fieldValue ? 'checked' : '' ?>>
                                                            <label class="form-check-label" for="<?= $fieldName ?>">
                                                                <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                                            </label>
                                                        </div>

                                                    <?php else: ?>
                                                        <input type="text" 
                                                               name="<?= $fieldName ?>" 
                                                               id="<?= $fieldName ?>" 
                                                               class="form-control <?= $isRequired ? 'required' : '' ?>" 
                                                               value="<?= htmlspecialchars($fieldValue) ?>"
                                                               placeholder="<?= $field['placeholder'] ?? '' ?>">
                                                    <?php endif; ?>

                                                    <?php if (!empty($field['help_text'])): ?>
                                                        <small class="form-text text-muted"><?= $field['help_text'] ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php $firstTab = false; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                لا توجد حقول محددة لهذه الشركة. يرجى إعداد الحقول أولاً.
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Form Actions -->
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                            <div>
                                <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Form validation
document.getElementById('productForm').addEventListener('submit', function(e) {
    const requiredFields = document.querySelectorAll('.required');
    let hasErrors = false;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            hasErrors = true;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (hasErrors) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});

// Reset form function
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        document.getElementById('productForm').reset();
    }
}

// Auto-save functionality (optional)
let autoSaveTimer;
document.getElementById('productForm').addEventListener('input', function() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
        // Auto-save logic here if needed
        console.log('Auto-saving...');
    }, 5000);
});
</script>
