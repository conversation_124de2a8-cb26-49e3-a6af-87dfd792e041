<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-boxes"></i> <?= __('إدارة المنتجات') ?>
                </h1>
                <div>
                    <?php if (canCreate('products')): ?>
                    <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-1"></i> <?= __('إضافة منتج جديد') ?>
                    </a>
                    <?php endif; ?>
                    <a href="<?= base_url('inventory') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمخزون') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php
    // استخدام Toastr بدلاً من display_flash
    $success = flash('product_success');
    if ($success) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.success("' . addslashes($success['message']) . '", "' . __('نجاح') . '");
                }
            });
        </script>';
    }

    $error = flash('product_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i> <?= __('إحصائيات المنتجات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-primary mb-2">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count($products) ?></h3>
                                    <p class="text-muted mb-0"><?= __('إجمالي المنتجات') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-success mb-2">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count(array_filter($products, fn($p) => $p['is_active'] == 1)) ?></h3>
                                    <p class="text-muted mb-0"><?= __('منتجات نشطة') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= $stats['low_stock_products'] ?? 0 ?></h3>
                                    <p class="text-muted mb-0"><?= __('منخفض المخزون') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-danger mb-2">
                                        <i class="fas fa-times-circle fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= $stats['out_of_stock_products'] ?? 0 ?></h3>
                                    <p class="text-muted mb-0"><?= __('نافد المخزون') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i> <?= __('قائمة المنتجات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="text-muted"><?= __('إجمالي المنتجات') ?>: <strong><?= count($products) ?></strong></span>
                        </div>
                        <div>
                            <?php if (canView('products')): ?>
                            <button class="btn btn-outline-success btn-sm me-2" onclick="exportProducts()">
                                <i class="fas fa-file-excel me-1"></i> <?= __('تصدير Excel') ?>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="printProducts()">
                                <i class="fas fa-print me-1"></i> <?= __('طباعة') ?>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>

        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات</h5>
                <p class="text-muted">ابدأ بإضافة منتجات جديدة لشركتك</p>
                <?php if (canCreate('products')): ?>
                <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول منتج
                </a>
                <?php else: ?>
                <p class="text-muted">ليس لديك صلاحية لإضافة منتجات</p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table id="productsTable" class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الصورة</th>
                            <th>كود المنتج</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>الوحدة</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($product['image_url'])): ?>
                                        <img src="<?= base_url($product['image_url']) ?>"
                                             alt="<?= htmlspecialchars($product['product_name_ar']) ?>"
                                             class="product-image">
                                    <?php else: ?>
                                        <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($product['product_code']) ?></strong>
                                    <?php if (!empty($product['barcode'])): ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($product['barcode']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($product['product_name_ar']) ?></strong>
                                    <?php if (!empty($product['product_name_en'])): ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($product['product_name_en']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($product['category_name_ar'] ?? 'غير محدد') ?></td>
                                <td><?= htmlspecialchars($product['unit_name_ar'] ?? 'غير محدد') ?></td>
                                <td><?= number_format($product['cost_price'], 2) ?> ر.س</td>
                                <td><?= number_format($product['selling_price'], 2) ?> ر.س</td>
                                <td>
                                    <?php
                                    $stock = $product['total_stock'] ?? 0;
                                    $min_stock = $product['min_stock_level'] ?? 0;
                                    $stock_class = $stock <= $min_stock ? 'stock-low' : 'stock-normal';
                                    ?>
                                    <span class="<?= $stock_class ?>">
                                        <?= number_format($stock, 2) ?>
                                        <?php if ($stock <= $min_stock): ?>
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($product['is_active']): ?>
                                        <span class="status-badge status-active">نشط</span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if (canView('products')): ?>
                                        <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>"
                                           class="btn btn-outline-info btn-action" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php endif; ?>

                                        <?php if (canEdit('products')): ?>
                                        <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>"
                                           class="btn btn-outline-warning btn-action" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>

                                        <?php if (canDelete('products')): ?>
                                        <button onclick="deleteProduct(<?= $product['product_id'] ?>)"
                                                class="btn btn-outline-danger btn-action" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة DataTables
    $('#productsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        pageLength: 25,
        responsive: true,
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, -1] }
        ]
    });
});

// حذف منتج
function deleteProduct(productId) {
    if (confirm('<?= __('هل أنت متأكد من حذف هذا المنتج؟') ?>')) {
        fetch(`<?= base_url('inventory/products/') ?>${productId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('<?= __('حدث خطأ أثناء حذف المنتج') ?>');
            }
        })
        .catch(error => {
            alert('<?= __('حدث خطأ أثناء حذف المنتج') ?>');
        });
    }
}

// تصدير المنتجات
function exportProducts() {
    window.open('<?= base_url('inventory/products/export') ?>', '_blank');
}

// طباعة المنتجات
function printProducts() {
    window.print();
}
</script>
