<?php
/**
 * نموذج تعديل فئة ديناميكي
 * يعرض النموذج حسب الحقول والتبويبات المختارة للشركة
 */
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-1">
                <i class="fas fa-edit text-warning me-2"></i>
                <?= $title ?>
            </h2>
            <p class="text-muted mb-0">تعديل فئة بالنظام الديناميكي</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة للقائمة
            </a>
            <a href="<?= base_url('inventory/categories/' . $category['category_id']) ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i>
                عرض الفئة
            </a>
        </div>
    </div>

    <!-- تشخيص المشكلة -->
    <?php if(empty($tabs)): ?>
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>لا توجد حقول مكونة</h5>
            <p>لم يتم العثور على حقول مكونة لهذه الشركة. يجب إعداد الحقول أولاً.</p>
            <a href="<?= base_url('inventory/settings/fields_category') ?>" class="btn btn-primary">
                <i class="fas fa-cog me-1"></i>إعداد الحقول الآن
            </a>
        </div>

        <!-- معلومات التشخيص -->
        <div class="card mt-3">
            <div class="card-header">معلومات التشخيص</div>
            <div class="card-body">
                <pre><?php
                echo "Company ID: " . ($company_id ?? 'غير محدد') . "\n";
                echo "Category ID: " . ($category['category_id'] ?? 'غير محدد') . "\n";
                echo "Tabs count: " . count($tabs ?? []) . "\n";
                echo "Parent Categories count: " . count($parentCategories ?? []) . "\n";
                ?></pre>
            </div>
        </div>
    <?php else: ?>

    <!-- نموذج التعديل -->
    <form method="POST" action="<?= base_url('inventory/categories/' . $category['category_id'] . '/update') ?>" id="categoryForm">
        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

        <div class="row">
            <!-- التبويبات الديناميكية -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <!-- Nav Tabs -->
                        <ul class="nav nav-tabs" id="categoryTabs" role="tablist">
                            <?php $firstTab = true; ?>
                            <?php foreach($tabs as $tab): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?= $firstTab ? 'active' : '' ?>" 
                                            id="tab-<?= $tab['tab_id'] ?>-tab" 
                                            data-bs-toggle="tab" 
                                            data-bs-target="#tab-<?= $tab['tab_id'] ?>" 
                                            type="button" role="tab">
                                        <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                        <?= $tab['tab_label_ar'] ?>
                                        <span class="badge bg-secondary ms-2"><?= count($tab['fields']) ?></span>
                                    </button>
                                </li>
                                <?php $firstTab = false; ?>
                            <?php endforeach; ?>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content mt-4" id="categoryTabsContent">
                            <?php $firstTab = true; ?>
                            <?php foreach($tabs as $tab): ?>
                                <div class="tab-pane fade <?= $firstTab ? 'show active' : '' ?>"
                                     id="tab-<?= $tab['tab_id'] ?>" role="tabpanel">

                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <h5 class="text-muted">
                                                <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                                <?= $tab['tab_label_ar'] ?>
                                            </h5>
                                            <hr>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <?php foreach($tab['fields'] as $field): ?>
                                            <?php if($field['is_visible_form'] && !$field['is_hidden']): ?>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">
                                                        <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                                        <?php if($field['is_required']): ?>
                                                            <span class="text-danger">*</span>
                                                        <?php endif; ?>
                                                    </label>

                                                    <?php
                                                    $fieldName = $field['field_name'];
                                                    $placeholder = $field['custom_placeholder_ar'] ?: $field['placeholder_ar'] ?: '';
                                                    $helpText = $field['custom_help_text_ar'] ?: $field['help_text_ar'] ?: '';
                                                    $required = $field['is_required'] ? 'required' : '';
                                                    // استخدام قيمة الفئة الحالية بدلاً من القيمة الافتراضية
                                                    $value = $category[$fieldName] ?? $field['default_value'] ?? '';
                                                    ?>

                                                    <?php if($field['input_type'] == 'text'): ?>
                                                        <input type="text" 
                                                               name="<?= $fieldName ?>" 
                                                               class="form-control <?= $required ?>" 
                                                               placeholder="<?= htmlspecialchars($placeholder) ?>"
                                                               value="<?= htmlspecialchars($value) ?>"
                                                               <?= $required ?>>

                                                    <?php elseif($field['input_type'] == 'number'): ?>
                                                        <input type="number" 
                                                               name="<?= $fieldName ?>" 
                                                               class="form-control <?= $required ?>" 
                                                               placeholder="<?= htmlspecialchars($placeholder) ?>"
                                                               value="<?= htmlspecialchars($value) ?>"
                                                               step="<?= $field['field_type'] == 'DECIMAL' ? '0.01' : '1' ?>"
                                                               min="0"
                                                               <?= $required ?>>

                                                    <?php elseif($field['input_type'] == 'textarea'): ?>
                                                        <textarea name="<?= $fieldName ?>" 
                                                                  class="form-control <?= $required ?>" 
                                                                  rows="3"
                                                                  placeholder="<?= htmlspecialchars($placeholder) ?>"
                                                                  <?= $required ?>><?= htmlspecialchars($value) ?></textarea>

                                                    <?php elseif($field['input_type'] == 'select'): ?>
                                                        <select name="<?= $fieldName ?>" 
                                                                class="form-select <?= $required ?>" 
                                                                <?= $required ?>>
                                                            <option value="">اختر...</option>
                                                            
                                                            <?php
                                                            // خيارات من الحقل
                                                            if(!empty($field['field_options'])) {
                                                                $options = json_decode($field['field_options'], true);
                                                                if(is_array($options)) {
                                                                    foreach($options as $option) {
                                                                        $selected = ($value == $option) ? 'selected' : '';
                                                                        echo "<option value=\"" . htmlspecialchars($option) . "\" {$selected}>" . htmlspecialchars($option) . "</option>";
                                                                    }
                                                                }
                                                            }
                                                            
                                                            // خيارات الفئة الأب
                                                            if($fieldName == 'parent_category_id' && !empty($parentCategories)) {
                                                                foreach($parentCategories as $parentCategory) {
                                                                    $selected = ($value == $parentCategory['category_id']) ? 'selected' : '';
                                                                    echo "<option value=\"{$parentCategory['category_id']}\" {$selected}>{$parentCategory['category_name_ar']}</option>";
                                                                }
                                                            }
                                                            ?>
                                                        </select>

                                                    <?php elseif($field['input_type'] == 'checkbox'): ?>
                                                        <div class="form-check">
                                                            <input type="checkbox" 
                                                                   name="<?= $fieldName ?>" 
                                                                   class="form-check-input" 
                                                                   id="<?= $fieldName ?>"
                                                                   value="1"
                                                                   <?= $value ? 'checked' : '' ?>>
                                                            <label class="form-check-label" for="<?= $fieldName ?>">
                                                                <?= $helpText ?: 'تفعيل هذا الخيار' ?>
                                                            </label>
                                                        </div>

                                                    <?php elseif($field['input_type'] == 'file'): ?>
                                                        <input type="file"
                                                               name="<?= $fieldName ?>"
                                                               class="form-control <?= $required ?>"
                                                               accept="<?= $field['field_options']['accept'] ?? 'image/*' ?>"
                                                               <?= $required ?>>

                                                    <?php elseif($field['input_type'] == 'color'): ?>
                                                        <input type="color"
                                                               name="<?= $fieldName ?>"
                                                               class="form-control form-control-color <?= $required ?>"
                                                               value="<?= htmlspecialchars($value ?: '#007bff') ?>"
                                                               <?= $required ?>>

                                                    <?php elseif($field['input_type'] == 'date'): ?>
                                                        <input type="date"
                                                               name="<?= $fieldName ?>"
                                                               class="form-control <?= $required ?>"
                                                               value="<?= htmlspecialchars($value) ?>"
                                                               <?= $required ?>>

                                                    <?php else: ?>
                                                        <!-- نوع حقل غير مدعوم -->
                                                        <input type="text" 
                                                               name="<?= $fieldName ?>" 
                                                               class="form-control <?= $required ?>" 
                                                               placeholder="<?= htmlspecialchars($placeholder) ?>"
                                                               value="<?= htmlspecialchars($value) ?>"
                                                               <?= $required ?>>
                                                    <?php endif; ?>

                                                    <?php if($helpText): ?>
                                                        <div class="form-text text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            <?= htmlspecialchars($helpText) ?>
                                                        </div>
                                                    <?php endif; ?>

                                                    <!-- عرض أخطاء التحقق -->
                                                    <?php if(isset($errors[$fieldName])): ?>
                                                        <div class="invalid-feedback d-block">
                                                            <?= $errors[$fieldName] ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php $firstTab = false; ?>
                            <?php endforeach; ?>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التعديلات
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a href="<?= base_url('inventory/categories/' . $category['category_id']) ?>" class="btn btn-outline-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض الفئة
                                        </a>
                                        <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-danger">
                                            <i class="fas fa-times me-1"></i>
                                            إلغاء
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التبويبات
    const tabTriggerList = [].slice.call(document.querySelectorAll('#categoryTabs button'));
    tabTriggerList.forEach(function (tabTriggerEl) {
        new bootstrap.Tab(tabTriggerEl);
    });

    // التحقق من النموذج
    const form = document.getElementById('categoryForm');
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }

        // إظهار مؤشر التحميل
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري حفظ التعديلات...';
        submitBtn.disabled = true;
    });

    // حفظ تلقائي كل دقيقتين
    setInterval(autoSave, 120000);
});

function validateForm() {
    let isValid = true;
    const requiredFields = document.querySelectorAll('.required');
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        alert('يرجى ملء جميع الحقول المطلوبة');
    }

    return isValid;
}

function autoSave() {
    const formData = new FormData(document.getElementById('categoryForm'));
    formData.append('auto_save', '1');

    fetch('<?= base_url('inventory/categories/' . $category['category_id'] . '/auto-save') ?>', {
        method: 'POST',
        body: formData
    }).then(response => {
        if (response.ok) {
            console.log('تم الحفظ التلقائي');
        }
    }).catch(error => {
        console.log('خطأ في الحفظ التلقائي:', error);
    });
}
</script>

<style>
.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
    margin-bottom: -1px;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1.5rem;
    border-radius: 0 0 0.5rem 0.5rem;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.required {
    border-left: 3px solid #dc3545;
}

.is-invalid {
    border-color: #dc3545;
}

.badge {
    font-size: 0.75rem;
}

.form-control-color {
    width: 60px;
    height: 38px;
}
</style>

<?php endif; ?>
