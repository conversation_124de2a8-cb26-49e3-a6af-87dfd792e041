<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;
use App\Core\FieldManager;

/**
 * Product Model - نموذج المنتجات
 */
class Product
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Field manager for dynamic fields
     */
    protected $fieldManager;

    /**
     * Table name
     */
    protected $table = 'inventory_products';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->fieldManager = new FieldManager($db);
    }

    /**
     * الحصول على جميع المنتجات للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?";

        $params = [$company_id];

        // تطبيق الفلاتر
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.product_name_ar LIKE ? OR p.product_name_en LIKE ? OR p.product_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        if (isset($filters['is_active'])) {
            $sql .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }

        $sql .= " GROUP BY p.product_id ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات مع تفاصيل كاملة للعرض في الجدول
     */
    public function getProductsWithDetails($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       c.category_name_en,
                       u.unit_name_ar,
                       u.unit_name_en,
                       u.unit_symbol_ar,
                       u.unit_symbol_en,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock,
                       COALESCE(SUM(s.quantity_available), 0) as available_stock,
                       COALESCE(SUM(s.quantity_reserved), 0) as reserved_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?
                GROUP BY p.product_id
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالمعرف
     */
    public function getById($product_id, $company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar, c.category_name_en,
                       u.unit_name_ar, u.unit_name_en, u.unit_symbol_ar, u.unit_symbol_en
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                WHERE p.product_id = ? AND p.company_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالكود
     */
    public function getByCode($product_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE product_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء منتج جديد
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['product_code'], $data['company_id'])) {
            throw new Exception('كود المنتج موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, product_code, barcode,
                    product_name_ar, product_name_en, description_ar, description_en,
                    category_id, unit_id, product_type, track_inventory,
                    cost_price, selling_price, min_stock_level, max_stock_level, reorder_point,
                    weight, dimensions, tax_rate, is_active, created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث منتج
     */
    public function update($product_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['product_code'])) {
            $existing = $this->getByCode($data['product_code'], $company_id);
            if ($existing && $existing['product_id'] != $product_id) {
                throw new Exception('كود المنتج موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    product_code = ?, barcode = ?,
                    product_name_ar = ?, product_name_en = ?,
                    description_ar = ?, description_en = ?,
                    category_id = ?, unit_id = ?, product_type = ?, track_inventory = ?,
                    cost_price = ?, selling_price = ?,
                    min_stock_level = ?, max_stock_level = ?, reorder_point = ?,
                    weight = ?, dimensions = ?, tax_rate = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE product_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['updated_by'],
            $product_id,
            $company_id
        ]);
    }

    /**
     * حذف منتج
     */
    public function delete($product_id, $company_id)
    {
        // التحقق من عدم وجود حركات للمنتج
        $sql = "SELECT COUNT(*) FROM inventory_movements WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
        }

        // حذف أرصدة المخزون أولاً
        $sql = "DELETE FROM inventory_stock WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        // حذف المنتج
        $sql = "DELETE FROM {$this->table} WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$product_id, $company_id]);
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock <= p.min_stock_level
                ORDER BY total_stock ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات النافدة
     */
    public function getOutOfStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock = 0
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على إحصائيات المنتجات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المنتجات
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_products'] = $stmt->fetchColumn();

        // المنتجات النشطة
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['active_products'] = $stmt->fetchColumn();

        // المنتجات منخفضة المخزون
        $stats['low_stock_count'] = count($this->getLowStockProducts($company_id));

        // المنتجات النافدة
        $stats['out_of_stock_count'] = count($this->getOutOfStockProducts($company_id));

        return $stats;
    }

    // ========================================
    // الدوال الديناميكية الجديدة
    // ========================================

    /**
     * الحصول على المنتجات مع القيم الديناميكية للعرض في الجداول
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicProducts($companyId, $tableFields, $filters = [])
    {
        // الحصول على معرفات المنتجات الفريدة للشركة
        $productIdsSql = "SELECT DISTINCT record_id
                         FROM dynamic_field_values
                         WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'products'
                         ORDER BY record_id DESC";

        if (!empty($filters['limit'])) {
            $productIdsSql .= " LIMIT " . (int)$filters['limit'];
        }

        $stmt = $this->db->prepare($productIdsSql);
        $stmt->execute([$companyId]);
        $productIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($productIds)) {
            return [];
        }

        $results = [];

        // جلب بيانات كل منتج
        foreach ($productIds as $productId) {
            $product = $this->getDynamicProduct($productId, $companyId, $tableFields);
            if ($product) {
                // تطبيق الفلاتر
                $includeProduct = true;

                // فلتر البحث
                if (!empty($filters['search'])) {
                    $includeProduct = false;
                    foreach($tableFields as $field) {
                        if ($field['is_searchable'] && isset($product[$field['field_name']])) {
                            if (stripos($product[$field['field_name']], $filters['search']) !== false) {
                                $includeProduct = true;
                                break;
                            }
                        }
                    }
                }

                // فلاتر الحقول
                if ($includeProduct) {
                    foreach($filters as $key => $value) {
                        if (strpos($key, 'filter_') === 0 && !empty($value)) {
                            $fieldName = str_replace('filter_', '', $key);
                            if (isset($product[$fieldName]) && $product[$fieldName] != $value) {
                                $includeProduct = false;
                                break;
                            }
                        }
                    }
                }

                if ($includeProduct) {
                    $results[] = $product;
                }
            }
        }

        // ترتيب النتائج
        if (!empty($filters['sort'])) {
            $sortField = $filters['sort'];
            $direction = (!empty($filters['direction']) && $filters['direction'] == 'desc') ? SORT_DESC : SORT_ASC;

            $sortValues = array_column($results, $sortField);
            array_multisort($sortValues, $direction, $results);
        }

        return $results;
    }

    /**
     * إنشاء منتج جديد مع النظام الديناميكي الكامل
     * لا يعتمد على أي جداول ثابتة - فقط dynamic_field_values
     */
    public function createDynamicProduct($companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // إنشاء معرف منتج جديد
            $productId = $this->generateNewProductId($companyId);

            // حفظ جميع قيم الحقول في النظام الديناميكي
            $this->saveAllDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return $productId;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * تحديث منتج مع النظام الديناميكي
     */
    public function updateDynamicProduct($productId, $companyId, $data, $userId)
    {
        try {
            $this->db->beginTransaction();

            // تحديث قيم الحقول الديناميكية
            $this->saveDynamicFieldValues($companyId, $productId, $data, $userId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على منتج مع قيمه الديناميكية
     * النظام الديناميكي الكامل - لا يعتمد على جداول ثابتة
     */
    public function getDynamicProduct($productId, $companyId, $fields = null)
    {
        // التحقق من وجود المنتج في النظام الديناميكي
        $checkSql = "SELECT COUNT(*) FROM dynamic_field_values
                     WHERE company_id = ? AND module_name = 'inventory'
                     AND table_name = 'products' AND record_id = ?";
        $stmt = $this->db->prepare($checkSql);
        $stmt->execute([$companyId, $productId]);

        if ($stmt->fetchColumn() == 0) {
            return null;
        }

        // الحصول على الحقول إذا لم يتم تمريرها
        if ($fields === null) {
            $fields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');
        }

        // إنشاء مصفوفة المنتج مع معرف المنتج
        $product = ['product_id' => $productId];

        // الحصول على قيم الحقول الديناميكية
        $fieldValues = $this->fieldManager->getFieldValues($companyId, 'inventory', 'products', $productId);

        // دمج جميع القيم من النظام الديناميكي
        foreach($fields as $field) {
            $fieldName = $field['field_name'];
            $value = $fieldValues[$fieldName] ?? $field['default_value'] ?? '';
            $product[$fieldName] = $this->formatFieldValue($value, $field);
        }

        return $product;
    }

    // ========================================
    // الدوال المساعدة للنظام الديناميكي الكامل
    // ========================================

    /**
     * إنشاء معرف منتج جديد للشركة
     */
    private function generateNewProductId($companyId)
    {
        // الحصول على أعلى معرف منتج للشركة
        $sql = "SELECT MAX(record_id) FROM dynamic_field_values
                WHERE company_id = ? AND module_name = 'inventory' AND table_name = 'products'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId]);
        $maxId = $stmt->fetchColumn();

        return ($maxId ? $maxId + 1 : 1);
    }

    /**
     * التحقق من عدم تكرار كود المنتج
     */
    private function checkProductCodeUnique($companyId, $productCode, $excludeProductId = null)
    {
        // البحث عن كود المنتج في النظام الديناميكي
        $sql = "SELECT dfv.record_id
                FROM dynamic_field_values dfv
                JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'products'
                AND sf.field_name = 'product_code'
                AND dfv.field_value = ?";

        $params = [$companyId, $productCode];

        if ($excludeProductId) {
            $sql .= " AND dfv.record_id != ?";
            $params[] = $excludeProductId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() === false;
    }

    /**
     * حفظ جميع قيم الحقول في النظام الديناميكي الكامل
     */
    private function saveAllDynamicFieldValues($companyId, $productId, $data, $userId)
    {
        // التحقق من كود المنتج
        if (empty($data['product_code'])) {
            throw new Exception('كود المنتج مطلوب');
        }

        if (!$this->checkProductCodeUnique($companyId, $data['product_code'])) {
            throw new Exception("كود المنتج '{$data['product_code']}' موجود مسبقاً");
        }

        // الحصول على الحقول المفعلة للشركة
        $enabledFields = $this->fieldManager->getCompanySelectedFields($companyId, 'inventory', 'products');

        foreach($enabledFields as $field) {
            $fieldName = $field['field_name'];

            // تحديد القيمة
            $value = '';
            if (isset($data[$fieldName])) {
                $value = $this->processFieldValue($data[$fieldName], $field);
            } elseif (!empty($field['default_value'])) {
                $value = $field['default_value'];
            } elseif ($field['is_required']) {
                throw new Exception("الحقل '{$field['field_label_ar']}' مطلوب");
            }

            // حفظ القيمة في النظام الديناميكي
            $this->fieldManager->saveFieldValue(
                $companyId,
                'inventory',
                'products',
                $productId,
                $field['field_id'],
                $value,
                $userId
            );
        }
    }

    /**
     * معالجة قيمة الحقل قبل الحفظ
     */
    private function processFieldValue($value, $field)
    {
        switch($field['field_type']) {
            case 'INT':
                return (int)$value;

            case 'DECIMAL':
                return (float)$value;

            case 'TINYINT':
                return $value ? 1 : 0;

            case 'DATE':
                return !empty($value) ? date('Y-m-d', strtotime($value)) : null;

            case 'TIMESTAMP':
                return !empty($value) ? date('Y-m-d H:i:s', strtotime($value)) : null;

            default:
                return trim($value);
        }
    }

    /**
     * تنسيق قيمة الحقل للعرض
     */
    private function formatFieldValue($value, $field)
    {
        if (empty($value)) {
            return '';
        }

        switch($field['field_type']) {
            case 'DECIMAL':
                $precision = 2;
                if (!empty($field['field_length'])) {
                    $parts = explode(',', $field['field_length']);
                    if (count($parts) == 2) {
                        $precision = (int)$parts[1];
                    }
                }
                return number_format((float)$value, $precision);

            case 'TINYINT':
                if ($field['input_type'] == 'checkbox') {
                    return $value ? 'نعم' : 'لا';
                }
                return $value;

            case 'DATE':
                return date('Y-m-d', strtotime($value));

            case 'TIMESTAMP':
                return date('Y-m-d H:i:s', strtotime($value));

            default:
                return htmlspecialchars($value);
        }
    }

    /**
     * حذف منتج من النظام الديناميكي الكامل
     */
    public function deleteDynamicProduct($productId, $companyId)
    {
        try {
            $this->db->beginTransaction();

            // حذف جميع قيم الحقول الديناميكية للمنتج
            $sql = "DELETE FROM dynamic_field_values
                    WHERE company_id = ? AND module_name = 'inventory'
                    AND table_name = 'products' AND record_id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$companyId, $productId]);

            if (!$result) {
                throw new Exception('فشل في حذف المنتج');
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * البحث في المنتجات الديناميكية
     */
    public function searchDynamicProducts($companyId, $searchTerm, $searchFields = null)
    {
        if ($searchFields === null) {
            $searchFields = $this->fieldManager->getCompanySearchableFields($companyId, 'inventory', 'products');
        }

        if (empty($searchFields)) {
            return [];
        }

        // البحث في النظام الديناميكي الكامل
        $searchConditions = [];
        $params = [$companyId];

        foreach($searchFields as $field) {
            $searchConditions[] = "(dfv.field_id = ? AND dfv.field_value LIKE ?)";
            $params[] = $field['field_id'];
            $params[] = '%' . $searchTerm . '%';
        }

        $sql = "SELECT DISTINCT dfv.record_id
                FROM dynamic_field_values dfv
                WHERE dfv.company_id = ?
                AND dfv.module_name = 'inventory'
                AND dfv.table_name = 'products'
                AND (" . implode(' OR ', $searchConditions) . ")
                ORDER BY dfv.record_id DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $productIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // جلب بيانات المنتجات المطابقة
        $results = [];
        foreach ($productIds as $productId) {
            $product = $this->getDynamicProduct($productId, $companyId);
            if ($product) {
                $results[] = $product;
            }
        }

        return $results;
    }
}
