<?php
namespace App\Modules\Inventory\Services;

use App\Modules\Inventory\Inventory\Models\Product;
use App\Core\FieldManager;
use Exception;

/**
 * Product Service - خدمة المنتجات
 * النظام الديناميكي الجديد
 */
class ProductService
{
    protected $productModel;
    protected $fieldManager;

    public function __construct()
    {
        global $db;
        $this->fieldManager = new FieldManager($db);
        $this->productModel = new Product($db, $this->fieldManager);
    }

    /**
     * الحصول على الفئات للشركة
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    public function getCategories($company_id)
    {
        // إرجاع فئات افتراضية حتى يتم تطوير نظام الفئات الديناميكي
        return [
            ['category_id' => 1, 'category_name_ar' => 'إلكترونيات'],
            ['category_id' => 2, 'category_name_ar' => 'كتب'],
            ['category_id' => 3, 'category_name_ar' => 'ملابس']
        ];
    }

    /**
     * الحصول على وحدات القياس للشركة
     * مبسط للنظام الديناميكي - سيتم تطويره لاحقاً
     */
    public function getUnits($company_id)
    {
        // إرجاع وحدات افتراضية حتى يتم تطوير نظام الوحدات الديناميكي
        return [
            ['unit_id' => 1, 'unit_name_ar' => 'قطعة'],
            ['unit_id' => 2, 'unit_name_ar' => 'كيلوجرام'],
            ['unit_id' => 3, 'unit_name_ar' => 'متر']
        ];
    }

    /**
     * الحصول على أرصدة المنتج
     * النظام الديناميكي الجديد
     */
    public function getProductStock($product_id, $company_id)
    {
        // جلب رصيد المنتج من النظام الديناميكي
        $product = $this->productModel->getDynamicProduct($product_id, $company_id);

        if (!$product) {
            return [];
        }

        // إرجاع رصيد افتراضي من النظام الديناميكي
        return [
            [
                'warehouse_name_ar' => 'المخزن الرئيسي',
                'quantity_on_hand' => $product['current_stock'] ?? 0,
                'quantity_available' => $product['current_stock'] ?? 0,
                'quantity_reserved' => 0,
                'last_updated' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * إنشاء منتج جديد مع المخزون الأولي
     * النظام الديناميكي الجديد
     */
    public function createProductWithStock($productData, $initialStock = [])
    {
        try {
            // إنشاء المنتج باستخدام النظام الديناميكي
            $user = current_user();
            $company_id = $user['current_company_id'];
            $user_id = $user['UserID'];

            $product_id = $this->productModel->createDynamicProduct($company_id, $productData, $user_id);

            if (!$product_id) {
                throw new Exception('فشل في إنشاء المنتج');
            }

            return $product_id;

        } catch (Exception $e) {
            throw $e;
        }
    }

    // تم حذف الدوال التي تعتمد على الجداول القديمة
    // سيتم تطوير نظام المخزون والحركات لاحقاً في النظام الديناميكي


}
