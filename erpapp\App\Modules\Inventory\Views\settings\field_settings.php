<?php
/**
 * صفحة إعدادات الحقول الديناميكية
 * تسمح للشركات بتخصيص الحقول والتبويبات
 */
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-1">
                <i class="fas fa-sliders-h text-primary me-2"></i>
                <?= $title ?>
            </h2>
            <p class="text-muted mb-0">خصص الحقول والتبويبات حسب احتياجات شركتك</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-info" onclick="previewSettings()">
                <i class="fas fa-eye me-1"></i>
                معاينة
            </button>
            <a href="<?= base_url($entityUrl) ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- تعليمات -->
    <div class="alert alert-info mb-4">
        <h6 class="alert-heading">
            <i class="fas fa-info-circle me-2"></i>
            كيفية الاستخدام:
        </h6>
        <ul class="mb-0">
            <li><strong>اختر الحقول:</strong> فعل الحقول التي تحتاجها شركتك</li>
            <li><strong>حدد الإعدادات:</strong> اختر أين تريد عرض كل حقل (نماذج، جداول، تفاصيل)</li>
            <li><strong>التبويبات التلقائية:</strong> ستظهر التبويبات تلقائياً حسب الحقول المختارة</li>
            <li><strong>التخصيص:</strong> يمكنك تغيير أسماء الحقول وترتيبها</li>
        </ul>
    </div>

    <!-- نموذج الإعدادات -->
    <form method="POST" action="<?= base_url('inventory/settings/fields/save') ?>" id="settingsForm">
        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
        <input type="hidden" name="module" value="<?= $module ?>">
        <input type="hidden" name="table" value="<?= $table ?>">

        <!-- التبويبات -->
        <div class="row">
            <?php foreach($tabs as $tab): ?>
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header" style="background-color: <?= $tab['tab_color'] ?>15;">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="<?= $tab['tab_icon'] ?> me-2" style="color: <?= $tab['tab_color'] ?>"></i>
                                    <?= $tab['tab_label_ar'] ?>
                                    <span class="badge bg-secondary ms-2"><?= count($tab['fields']) ?> حقل</span>
                                </h5>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input tab-toggle" 
                                           id="tab_<?= $tab['tab_id'] ?>" 
                                           data-tab="<?= $tab['tab_id'] ?>">
                                    <label class="form-check-label" for="tab_<?= $tab['tab_id'] ?>">
                                        تفعيل جميع الحقول
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach($tab['fields'] as $field): ?>
                                    <?php 
                                    $fieldId = $field['field_id'];
                                    $currentSettings = $companySettings[$fieldId] ?? [];
                                    $isEnabled = !empty($currentSettings['is_enabled']);
                                    ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card field-card <?= $isEnabled ? 'border-success' : '' ?>">
                                            <div class="card-body p-3">
                                                <!-- تفعيل الحقل -->
                                                <div class="form-check mb-3">
                                                    <input type="checkbox" 
                                                           name="fields[<?= $fieldId ?>][is_enabled]" 
                                                           class="form-check-input field-toggle" 
                                                           id="field_<?= $fieldId ?>"
                                                           value="1"
                                                           data-tab="<?= $tab['tab_id'] ?>"
                                                           <?= $isEnabled ? 'checked' : '' ?>>
                                                    <label class="form-check-label fw-bold" for="field_<?= $fieldId ?>">
                                                        <?= $field['field_label_ar'] ?>
                                                        <?php if($field['is_core_field']): ?>
                                                            <span class="badge bg-warning text-dark ms-1">أساسي</span>
                                                        <?php endif; ?>
                                                        <?php if($field['is_required_default']): ?>
                                                            <span class="badge bg-danger ms-1">مطلوب</span>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>

                                                <!-- إعدادات الحقل -->
                                                <div class="field-settings" style="display: <?= $isEnabled ? 'block' : 'none' ?>">
                                                    <!-- التسمية المخصصة -->
                                                    <div class="mb-2">
                                                        <label class="form-label small">التسمية المخصصة:</label>
                                                        <input type="text" 
                                                               name="fields[<?= $fieldId ?>][custom_label_ar]" 
                                                               class="form-control form-control-sm"
                                                               placeholder="<?= $field['field_label_ar'] ?>"
                                                               value="<?= htmlspecialchars($currentSettings['custom_label_ar'] ?? '') ?>">
                                                    </div>

                                                    <!-- خيارات العرض -->
                                                    <div class="row mb-2">
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_visible_form]" 
                                                                       class="form-check-input" 
                                                                       id="form_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_visible_form']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="form_<?= $fieldId ?>">
                                                                    في النماذج
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_visible_table]" 
                                                                       class="form-check-input" 
                                                                       id="table_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_visible_table']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="table_<?= $fieldId ?>">
                                                                    في الجداول
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-2">
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_required]" 
                                                                       class="form-check-input" 
                                                                       id="required_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_required']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="required_<?= $fieldId ?>">
                                                                    مطلوب
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_searchable]" 
                                                                       class="form-check-input" 
                                                                       id="search_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_searchable']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="search_<?= $fieldId ?>">
                                                                    قابل للبحث
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_filterable]" 
                                                                       class="form-check-input" 
                                                                       id="filter_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_filterable']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="filter_<?= $fieldId ?>">
                                                                    قابل للفلترة
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="form-check form-check-sm">
                                                                <input type="checkbox" 
                                                                       name="fields[<?= $fieldId ?>][is_sortable]" 
                                                                       class="form-check-input" 
                                                                       id="sort_<?= $fieldId ?>"
                                                                       value="1"
                                                                       <?= !empty($currentSettings['is_sortable']) ? 'checked' : '' ?>>
                                                                <label class="form-check-label small" for="sort_<?= $fieldId ?>">
                                                                    قابل للترتيب
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- معلومات الحقل -->
                                                    <div class="mt-2 pt-2 border-top">
                                                        <small class="text-muted">
                                                            <strong>النوع:</strong> <?= $field['field_type'] ?> |
                                                            <strong>الإدخال:</strong> <?= $field['input_type'] ?>
                                                        </small>
                                                        <?php if($field['help_text_ar']): ?>
                                                            <br><small class="text-info"><?= $field['help_text_ar'] ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- أزرار الحفظ -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الإعدادات
                                </button>
                                <button type="submit" name="save_and_continue" value="1" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    حفظ والمتابعة
                                </button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-outline-warning" onclick="resetToDefault()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين افتراضي
                                </button>
                                <a href="<?= base_url($entityUrl) ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Modal المعاينة -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الإعدادات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل/إلغاء تفعيل جميع حقول التبويب
    document.querySelectorAll('.tab-toggle').forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            const tabId = this.dataset.tab;
            const isChecked = this.checked;
            
            document.querySelectorAll(`.field-toggle[data-tab="${tabId}"]`).forEach(function(fieldToggle) {
                fieldToggle.checked = isChecked;
                toggleFieldSettings(fieldToggle);
            });
        });
    });

    // تفعيل/إلغاء تفعيل إعدادات الحقل
    document.querySelectorAll('.field-toggle').forEach(function(toggle) {
        toggle.addEventListener('change', function() {
            toggleFieldSettings(this);
            updateTabToggle(this.dataset.tab);
        });
    });

    // تحديث حالة تبويب عند تغيير حقل
    function updateTabToggle(tabId) {
        const tabFields = document.querySelectorAll(`.field-toggle[data-tab="${tabId}"]`);
        const checkedFields = document.querySelectorAll(`.field-toggle[data-tab="${tabId}"]:checked`);
        const tabToggle = document.querySelector(`.tab-toggle[data-tab="${tabId}"]`);
        
        if (checkedFields.length === 0) {
            tabToggle.checked = false;
            tabToggle.indeterminate = false;
        } else if (checkedFields.length === tabFields.length) {
            tabToggle.checked = true;
            tabToggle.indeterminate = false;
        } else {
            tabToggle.checked = false;
            tabToggle.indeterminate = true;
        }
    }

    // إظهار/إخفاء إعدادات الحقل
    function toggleFieldSettings(toggle) {
        const fieldCard = toggle.closest('.field-card');
        const settings = fieldCard.querySelector('.field-settings');
        
        if (toggle.checked) {
            settings.style.display = 'block';
            fieldCard.classList.add('border-success');
        } else {
            settings.style.display = 'none';
            fieldCard.classList.remove('border-success');
        }
    }

    // تحديث حالة التبويبات عند التحميل
    document.querySelectorAll('.tab-toggle').forEach(function(toggle) {
        updateTabToggle(toggle.dataset.tab);
    });
});

function previewSettings() {
    // جمع الحقول المختارة
    const selectedFields = [];
    document.querySelectorAll('.field-toggle:checked').forEach(function(field) {
        selectedFields.push(field.name);
    });

    if (selectedFields.length === 0) {
        alert('يرجى اختيار حقل واحد على الأقل للمعاينة');
        return;
    }

    // عرض المعاينة
    document.getElementById('previewContent').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">جاري تحميل المعاينة...</p>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();

    // محاكاة المعاينة
    setTimeout(() => {
        document.getElementById('previewContent').innerHTML = `
            <div class="alert alert-success">
                <h6>تم اختيار ${selectedFields.length} حقل</h6>
                <p>ستظهر التبويبات التي تحتوي على الحقول المختارة تلقائياً في النماذج والجداول.</p>
            </div>
        `;
    }, 1000);
}

function resetToDefault() {
    if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات للافتراضية؟\nسيتم فقدان جميع التخصيصات الحالية.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('inventory/settings/fields/reset') ?>';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= csrf_token() ?>';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
.field-card {
    transition: all 0.3s ease;
}

.field-card:hover {
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
}

.form-check-sm .form-check-input {
    margin-top: 0.125rem;
}

.form-check-sm .form-check-label {
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.border-success {
    border-color: #198754 !important;
    border-width: 2px !important;
}
</style>
