<?php

namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Models\Category;
use App\Core\FieldManager;

/**
 * Category Controller - متحكم الفئات
 * النظام الديناميكي الجديد - مطابق لنمط ProductController
 */
class CategoryController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Category model
     */
    protected $categoryModel;

    /**
     * Field manager
     */
    protected $fieldManager;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->categoryModel = new Category();

        // إنشاء مدير الحقول الديناميكية
        global $db;
        $this->fieldManager = new FieldManager($db);

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض قائمة الفئات الديناميكية
     */
    public function index()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'categories');

            if (!$hasFieldSettings) {
                // توجيه الشركة لإعداد الحقول أولاً
                view('Inventory::categories/setup_required', [
                    'title' => 'إعداد حقول الفئات مطلوب',
                    'message' => 'يجب إعداد حقول الفئات أولاً قبل البدء في استخدام النظام',
                    'setup_url' => base_url('inventory/settings/fields')
                ]);
                return;
            }

            // الحصول على الحقول المرئية في الجداول
            $tableFields = $this->fieldManager->getCompanyTableFields($company_id, 'inventory', 'categories');

            // الحصول على الحقول القابلة للبحث والفلترة
            $searchableFields = $this->fieldManager->getCompanySearchableFields($company_id, 'inventory', 'categories');
            $filterableFields = $this->fieldManager->getCompanyFilterableFields($company_id, 'inventory', 'categories');

            // الحصول على الفئات مع القيم الديناميكية
            $categories = $this->categoryModel->getDynamicCategories($company_id, $tableFields, $_GET);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إدارة الفئات - نظام ديناميكي',
                'categories' => $categories,
                'tableFields' => $tableFields,
                'searchableFields' => $searchableFields,
                'filterableFields' => $filterableFields,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'الفئات', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::categories/dynamic_index', $data);

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ أثناء تحميل الفئات: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory'));
        }
    }

    /**
     * عرض نموذج إضافة فئة جديدة
     */
    public function create()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'categories');

            if (!$hasFieldSettings) {
                redirect(base_url('inventory/settings/fields?message=setup_required'));
                return;
            }

            // الحصول على التبويبات والحقول المرئية في النماذج
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'categories', 'form');

            // الحصول على الحقول المطلوبة
            $requiredFields = $this->fieldManager->getCompanyRequiredFields($company_id, 'inventory', 'categories');
            $requiredFieldNames = array_column($requiredFields, 'field_name');

            // الحصول على البيانات المساعدة للحقول المرتبطة
            $parentCategories = $this->categoryModel->getDynamicCategories($company_id);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'إضافة فئة جديدة - نظام ديناميكي',
                'tabs' => $tabs,
                'requiredFields' => $requiredFieldNames,
                'parentCategories' => $parentCategories,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'الفئات', 'url' => base_url('inventory/categories')],
                    ['title' => 'إضافة فئة', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::categories/dynamic_create', $data);

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ أثناء تحميل النموذج: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories'));
        }
    }

    /**
     * حفظ فئة جديدة
     */
    public function store()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/categories/create'));
            }

            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            // التحقق من البيانات الديناميكية
            $this->validateDynamicCategoryData($_POST, $company_id);

            // إنشاء الفئة باستخدام النظام الديناميكي
            $category_id = $this->categoryModel->createDynamicCategory($company_id, $_POST, $user_id);

            if ($category_id) {
                flash('category_success', 'تم إضافة الفئة بنجاح', 'success');

                // التحقق من وجود زر "حفظ وإضافة آخر"
                if (isset($_POST['save_and_new'])) {
                    redirect(base_url('inventory/categories/create'));
                } else {
                    redirect(base_url('inventory/categories'));
                }
            } else {
                flash('category_error', 'حدث خطأ أثناء إضافة الفئة', 'danger');
                redirect(base_url('inventory/categories/create'));
            }

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories/create'));
        }
    }

    /**
     * عرض تفاصيل فئة
     */
    public function show()
    {
        try {
            $category_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'];

            if (!$category_id) {
                flash('category_error', 'معرف الفئة مطلوب', 'danger');
                redirect(base_url('inventory/categories'));
                return;
            }

            // التحقق من وجود إعدادات حقول للشركة
            $hasFieldSettings = $this->fieldManager->hasCompanyFieldSettings($company_id, 'inventory', 'categories');

            if (!$hasFieldSettings) {
                redirect(base_url('inventory/settings/fields?message=setup_required'));
                return;
            }

            // الحصول على التبويبات والحقول المرئية في صفحة التفاصيل (نفس طريقة التعديل)
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'categories', 'form');

            // الحصول على الحقول المرئية في صفحة التفاصيل
            $detailFields = $this->fieldManager->getCompanyDetailFields($company_id, 'inventory', 'categories');

            // الحصول على بيانات الفئة (بدون تحديد حقول معينة لجلب جميع البيانات)
            $category = $this->categoryModel->getDynamicCategory($category_id, $company_id);

            if (!$category) {
                flash('category_error', 'الفئة غير موجودة', 'danger');
                redirect(base_url('inventory/categories'));
                return;
            }

            // الحصول على البيانات المساعدة للحقول المرتبطة
            $parentCategories = $this->categoryModel->getDynamicCategories($company_id);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            $data = [
                'title' => 'تفاصيل الفئة - ' . ($category['category_name_ar'] ?? 'فئة'),
                'category' => $category,
                'tabs' => $tabs,
                'detailFields' => $detailFields,
                'parentCategories' => $parentCategories,
                'company' => $company,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'الفئات', 'url' => base_url('inventory/categories')],
                    ['title' => 'تفاصيل الفئة', 'active' => true]
                ]
            ];

            // استخدام العرض الديناميكي
            view('Inventory::categories/dynamic_show', $data);

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ أثناء تحميل الفئة: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories'));
        }
    }

    /**
     * عرض نموذج تعديل فئة
     */
    public function edit()
    {
        try {
            $category_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if (!$category_id) {
                flash('category_error', 'الفئة غير موجودة', 'danger');
                redirect(base_url('inventory/categories'));
            }

            // الحصول على الفئة من النظام الديناميكي
            $category = $this->categoryModel->getDynamicCategory($category_id, $company_id);

            if (!$category) {
                flash('category_error', 'الفئة غير موجودة', 'danger');
                redirect(base_url('inventory/categories'));
            }

            // الحصول على التبويبات والحقول المرئية في النماذج
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'categories', 'form');

            // الحصول على الحقول المطلوبة
            $requiredFields = $this->fieldManager->getCompanyRequiredFields($company_id, 'inventory', 'categories');
            $requiredFieldNames = array_column($requiredFields, 'field_name');

            // الحصول على البيانات المساعدة للحقول المرتبطة
            $parentCategories = $this->categoryModel->getDynamicCategories($company_id);

            $data = [
                'title' => 'تعديل الفئة - ' . ($category['category_name_ar'] ?? 'فئة'),
                'category' => $category,
                'tabs' => $tabs,
                'requiredFields' => $requiredFieldNames,
                'parentCategories' => $parentCategories,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'الفئات', 'url' => base_url('inventory/categories')],
                    ['title' => 'تعديل الفئة', 'active' => true]
                ]
            ];

            view('Inventory::categories/dynamic_edit', $data);

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories'));
        }
    }

    /**
     * تحديث فئة
     */
    public function update()
    {
        try {
            $category_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$category_id) {
                redirect(base_url('inventory/categories'));
            }

            // التحقق من وجود الفئة
            $category = $this->categoryModel->getDynamicCategory($category_id, $company_id);
            if (!$category) {
                flash('category_error', 'الفئة غير موجودة', 'danger');
                redirect(base_url('inventory/categories'));
            }

            // التحقق من البيانات الديناميكية
            $this->validateDynamicCategoryData($_POST, $company_id);

            // تحديث الفئة باستخدام النظام الديناميكي
            if ($this->categoryModel->updateDynamicCategory($category_id, $company_id, $_POST, $user_id)) {
                flash('category_success', 'تم تحديث الفئة بنجاح', 'success');
                redirect(base_url('inventory/categories/' . $category_id));
            } else {
                flash('category_error', 'حدث خطأ أثناء تحديث الفئة', 'danger');
                redirect(base_url('inventory/categories/' . $category_id . '/edit'));
            }

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories'));
        }
    }

    /**
     * حذف فئة
     */
    public function delete()
    {
        try {
            $category_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$category_id) {
                redirect(base_url('inventory/categories'));
            }

            // التحقق من وجود الفئة
            $category = $this->categoryModel->getDynamicCategory($category_id, $company_id);
            if (!$category) {
                flash('category_error', 'الفئة غير موجودة', 'danger');
                redirect(base_url('inventory/categories'));
            }

            // حذف الفئة من النظام الديناميكي
            if ($this->categoryModel->deleteDynamicCategory($category_id, $company_id)) {
                flash('category_success', 'تم حذف الفئة بنجاح', 'success');
            } else {
                flash('category_error', 'حدث خطأ أثناء حذف الفئة', 'danger');
            }

            redirect(base_url('inventory/categories'));

        } catch (Exception $e) {
            flash('category_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/categories'));
        }
    }

    /**
     * التحقق من صحة البيانات الديناميكية للفئة
     */
    private function validateDynamicCategoryData($data, $company_id, $category_id = null)
    {
        // الحصول على الحقول المطلوبة
        $requiredFields = $this->fieldManager->getCompanyRequiredFields($company_id, 'inventory', 'categories');

        foreach ($requiredFields as $field) {
            $fieldName = $field['field_name'];

            if (empty($data[$fieldName])) {
                throw new Exception($field['field_label_ar'] . ' مطلوب');
            }
        }

        // التحقق من تفرد كود الفئة
        if (!empty($data['category_code'])) {
            $existingCategory = $this->categoryModel->getCategoryByCode($data['category_code'], $company_id, $category_id);
            if ($existingCategory) {
                throw new Exception('كود الفئة موجود مسبقاً');
            }
        }
    }

    /**
     * البحث في الفئات
     */
    public function search()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('inventory/categories');
                return;
            }

            $company_id = $_SESSION['company_id'] ?? null;
            $searchTerm = $_POST['search'] ?? '';

            if (!$company_id) {
                redirect('auth/login');
                return;
            }

            // الحصول على الحقول المرئية في الجداول
            $tableFields = $this->fieldManager->getCompanyTableFields($company_id, 'inventory', 'categories');

            // البحث في الفئات
            $categories = $this->categoryModel->searchDynamicCategories($company_id, $searchTerm, $tableFields);

            $data = [
                'title' => 'نتائج البحث - ' . htmlspecialchars($searchTerm),
                'categories' => $categories,
                'tableFields' => $tableFields,
                'searchTerm' => $searchTerm,
                'company_id' => $company_id,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'الفئات', 'url' => base_url('inventory/categories')],
                    ['title' => 'نتائج البحث', 'active' => true]
                ]
            ];

            $this->view('inventory/categories/dynamic_index', $data);

        } catch (Exception $e) {
            error_log("خطأ في البحث في الفئات: " . $e->getMessage());
            $_SESSION['error'] = 'حدث خطأ في البحث';
            redirect('inventory/categories');
        }
    }

    /**
     * حفظ تلقائي للفئة
     */
    public function autoSave()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                return;
            }

            $company_id = $_SESSION['company_id'] ?? null;
            $user_id = $_SESSION['user_id'] ?? null;

            if (!$company_id || !$user_id) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }

            // حفظ البيانات في الجلسة للاستخدام لاحقاً
            $_SESSION['category_auto_save'] = $_POST;
            $_SESSION['category_auto_save_time'] = time();

            echo json_encode(['success' => true, 'message' => 'تم الحفظ التلقائي']);

        } catch (Exception $e) {
            error_log("خطأ في الحفظ التلقائي للفئة: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error']);
        }
    }

    /**
     * تصدير الفئات
     */
    public function export()
    {
        try {
            $company_id = $_SESSION['company_id'] ?? null;

            if (!$company_id) {
                redirect('auth/login');
                return;
            }

            // الحصول على جميع الفئات
            $categories = $this->categoryModel->getDynamicCategories($company_id);

            // إعداد headers للتحميل
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="categories_' . date('Y-m-d') . '.csv"');

            // إنشاء ملف CSV
            $output = fopen('php://output', 'w');

            // كتابة BOM للدعم العربي
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            // كتابة العناوين
            if (!empty($categories)) {
                fputcsv($output, array_keys($categories[0]));

                // كتابة البيانات
                foreach ($categories as $category) {
                    fputcsv($output, $category);
                }
            }

            fclose($output);

        } catch (Exception $e) {
            error_log("خطأ في تصدير الفئات: " . $e->getMessage());
            $_SESSION['error'] = 'حدث خطأ في تصدير الفئات';
            redirect('inventory/categories');
        }
    }

    /**
     * API: الحصول على قائمة الفئات
     */
    public function apiIndex()
    {
        try {
            $company_id = $_SESSION['company_id'] ?? null;

            if (!$company_id) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }

            $categories = $this->categoryModel->getDynamicCategories($company_id);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $categories,
                'count' => count($categories)
            ]);

        } catch (Exception $e) {
            error_log("خطأ في API الفئات: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error']);
        }
    }

    /**
     * API: الحصول على فئة واحدة
     */
    public function apiShow($category_id)
    {
        try {
            $company_id = $_SESSION['company_id'] ?? null;

            if (!$company_id) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }

            $category = $this->categoryModel->getDynamicCategory($category_id, $company_id);

            if (!$category) {
                http_response_code(404);
                echo json_encode(['error' => 'Category not found']);
                return;
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $category
            ]);

        } catch (Exception $e) {
            error_log("خطأ في API عرض الفئة: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error']);
        }
    }

    /**
     * API: البحث في الفئات
     */
    public function apiSearch()
    {
        try {
            $company_id = $_SESSION['company_id'] ?? null;
            $searchTerm = $_POST['search'] ?? '';

            if (!$company_id) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }

            $categories = $this->categoryModel->searchDynamicCategories($company_id, $searchTerm);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $categories,
                'count' => count($categories),
                'search_term' => $searchTerm
            ]);

        } catch (Exception $e) {
            error_log("خطأ في API البحث في الفئات: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error']);
        }
    }

    /**
     * API: الحصول على الفئات للقائمة المنسدلة
     */
    public function apiSelect()
    {
        try {
            $company_id = $_SESSION['company_id'] ?? null;

            if (!$company_id) {
                http_response_code(401);
                echo json_encode(['error' => 'Unauthorized']);
                return;
            }

            $categories = $this->categoryModel->getCategoriesForSelect($company_id);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $categories
            ]);

        } catch (Exception $e) {
            error_log("خطأ في API قائمة الفئات: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Server error']);
        }
    }

    /**
     * عرض رسالة خطأ
     */
    private function showError($message)
    {
        $data = [
            'title' => 'خطأ',
            'message' => $message,
            'breadcrumb' => [
                ['title' => 'المخزون', 'url' => base_url('inventory')],
                ['title' => 'الفئات', 'url' => base_url('inventory/categories')],
                ['title' => 'خطأ', 'active' => true]
            ]
        ];

        $this->view('errors/general', $data);
    }
}
