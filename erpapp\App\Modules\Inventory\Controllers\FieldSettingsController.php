<?php
namespace App\Modules\Inventory\Controllers;

use App\Core\DynamicTableManager;

/**
 * كنترولر إعدادات الحقول الديناميكية
 */
class FieldSettingsController
{
    private $dynamicManager;
    private $db;
    
    public function __construct()
    {
        global $db;
        $this->db = $db;
        $this->dynamicManager = new DynamicTableManager($db);
    }
    
    /**
     * عرض صفحة إعدادات الحقول
     */
    public function index()
    {
        // التحقق من الصلاحيات
        if (!check_permission('inventory_settings', 'view')) {
            redirect('/dashboard?error=no_permission');
            return;
        }
        
        $companyId = $_SESSION['company_id'];
        $moduleCode = $_GET['module'] ?? 'inventory';
        $entityType = $_GET['entity'] ?? 'product';
        
        try {
            // الحصول على جميع الحقول المتاحة
            $availableFields = $this->dynamicManager->getAvailableFields($moduleCode, $entityType);
            
            // تجميع الحقول حسب الفئة
            $fieldsByCategory = [];
            foreach ($availableFields as $field) {
                $fieldsByCategory[$field['category']][] = $field;
            }
            
            // الحصول على الحقول المفعلة للشركة
            $enabledFields = $this->dynamicManager->getCompanyEnabledFields($companyId, $moduleCode, $entityType);
            $enabledFieldIds = array_column($enabledFields, 'field_id');
            
            // التحقق من وجود جدول للشركة
            $hasTable = $this->dynamicManager->hasCompanyTable($companyId, $moduleCode, $entityType);
            
            return view('Inventory::settings/fields', [
                'fieldsByCategory' => $fieldsByCategory,
                'enabledFieldIds' => $enabledFieldIds,
                'enabledFields' => $enabledFields,
                'moduleCode' => $moduleCode,
                'entityType' => $entityType,
                'hasTable' => $hasTable,
                'categoryNames' => [
                    'core' => 'الحقول الأساسية',
                    'basic' => 'المعلومات الأساسية',
                    'classification' => 'التصنيف',
                    'pricing' => 'الأسعار',
                    'inventory' => 'المخزون',
                    'physical' => 'الخصائص الفيزيائية',
                    'warranty' => 'الضمان',
                    'dates' => 'التواريخ',
                    'supplier' => 'معلومات المورد',
                    'shipping' => 'الشحن',
                    'media' => 'الوسائط',
                    'tax' => 'الضرائب'
                ]
            ]);
            
        } catch (Exception $e) {
            return view('Inventory::settings/fields', [
                'error' => 'حدث خطأ في تحميل الإعدادات: ' . $e->getMessage(),
                'fieldsByCategory' => [],
                'enabledFieldIds' => [],
                'enabledFields' => [],
                'moduleCode' => $moduleCode,
                'entityType' => $entityType,
                'hasTable' => false,
                'categoryNames' => []
            ]);
        }
    }
    
    /**
     * تحديث إعدادات الحقول
     */
    public function updateFields()
    {
        // التحقق من الصلاحيات
        if (!check_permission('inventory_settings', 'edit')) {
            return json_response(['success' => false, 'message' => 'ليس لديك صلاحية لتعديل الإعدادات']);
        }
        
        $companyId = $_SESSION['company_id'];
        $userId = $_SESSION['user_id'];
        $moduleCode = $_POST['module_code'] ?? 'inventory';
        $entityType = $_POST['entity_type'] ?? 'product';
        
        try {
            // الحصول على الحقول المرسلة
            $fieldSettings = $_POST['fields'] ?? [];
            
            // الحصول على جميع الحقول المتاحة
            $availableFields = $this->dynamicManager->getAvailableFields($moduleCode, $entityType);
            
            $this->db->beginTransaction();
            
            // تحديث إعدادات كل حقل
            foreach ($availableFields as $field) {
                $fieldId = $field['field_id'];
                $isEnabled = isset($fieldSettings[$fieldId]['enabled']) ? 1 : 0;
                
                // الحقول الأساسية يجب أن تبقى مفعلة
                if ($field['is_core_field'] == 1) {
                    $isEnabled = 1;
                }
                
                $options = [
                    'is_required' => isset($fieldSettings[$fieldId]['required']) ? 1 : 0,
                    'is_visible' => isset($fieldSettings[$fieldId]['visible']) ? 1 : 0,
                    'is_searchable' => isset($fieldSettings[$fieldId]['searchable']) ? 1 : 0,
                    'is_listable' => isset($fieldSettings[$fieldId]['listable']) ? 1 : 0,
                    'display_order' => intval($fieldSettings[$fieldId]['order'] ?? $field['display_order']),
                    'custom_label_ar' => trim($fieldSettings[$fieldId]['label_ar'] ?? ''),
                    'custom_label_en' => trim($fieldSettings[$fieldId]['label_en'] ?? ''),
                    'field_group' => trim($fieldSettings[$fieldId]['group'] ?? $field['category'])
                ];
                
                // إزالة القيم الفارغة
                $options = array_filter($options, function($value) {
                    return $value !== '';
                });
                
                $this->dynamicManager->toggleFieldForCompany($companyId, $fieldId, $isEnabled, $userId, $options);
            }
            
            $this->db->commit();
            
            return json_response([
                'success' => true, 
                'message' => 'تم تحديث إعدادات الحقول بنجاح'
            ]);
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return json_response([
                'success' => false, 
                'message' => 'فشل في تحديث الإعدادات: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * إنشاء الجدول الديناميكي
     */
    public function createTable()
    {
        // التحقق من الصلاحيات
        if (!check_permission('inventory_settings', 'create')) {
            return json_response(['success' => false, 'message' => 'ليس لديك صلاحية لإنشاء الجداول']);
        }
        
        $companyId = $_SESSION['company_id'];
        $userId = $_SESSION['user_id'];
        $moduleCode = $_POST['module_code'] ?? 'inventory';
        $entityType = $_POST['entity_type'] ?? 'product';
        
        try {
            $tableName = $this->dynamicManager->createCompanyTable($companyId, $moduleCode, $entityType, $userId);
            
            return json_response([
                'success' => true,
                'message' => 'تم إنشاء الجدول بنجاح',
                'table_name' => $tableName
            ]);
            
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => 'فشل في إنشاء الجدول: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * إنشاء إعدادات افتراضية للشركة الجديدة
     */
    public function createDefaultSettings()
    {
        // التحقق من الصلاحيات
        if (!check_permission('inventory_settings', 'create')) {
            return json_response(['success' => false, 'message' => 'ليس لديك صلاحية لإنشاء الإعدادات']);
        }
        
        $companyId = $_SESSION['company_id'];
        $userId = $_SESSION['user_id'];
        $moduleCode = $_POST['module_code'] ?? 'inventory';
        $entityType = $_POST['entity_type'] ?? 'product';
        
        try {
            $fieldsCount = $this->dynamicManager->createDefaultSettingsForCompany($companyId, $userId, $moduleCode, $entityType);
            
            return json_response([
                'success' => true,
                'message' => "تم إنشاء الإعدادات الافتراضية بنجاح ({$fieldsCount} حقل)",
                'fields_count' => $fieldsCount
            ]);
            
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => 'فشل في إنشاء الإعدادات الافتراضية: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * معاينة هيكل الجدول
     */
    public function previewTable()
    {
        $companyId = $_SESSION['company_id'];
        $moduleCode = $_GET['module'] ?? 'inventory';
        $entityType = $_GET['entity'] ?? 'product';
        
        try {
            $enabledFields = $this->dynamicManager->getCompanyEnabledFields($companyId, $moduleCode, $entityType);
            
            return json_response([
                'success' => true,
                'fields' => $enabledFields,
                'table_name' => "company_{$companyId}_{$moduleCode}_{$entityType}"
            ]);
            
        } catch (Exception $e) {
            return json_response([
                'success' => false,
                'message' => 'فشل في معاينة الجدول: ' . $e->getMessage()
            ]);
        }
    }
}
