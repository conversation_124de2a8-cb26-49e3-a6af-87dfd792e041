<?php
namespace App\Modules\Inventory\Controllers;

use App\Core\FieldManager;
use Exception;

/**
 * Field Settings Controller - متحكم إعدادات الحقول
 * يدير إعدادات الحقول الديناميكية للشركات
 */
class FieldSettingsController
{
    /**
     * Field manager
     */
    protected $fieldManager;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->fieldManager = new FieldManager($db);

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض صفحة إعدادات الحقول
     */
    public function index()
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // الحصول على معاملات module و table من URL
            $module = $_GET['module'] ?? 'inventory';
            $table = $_GET['table'] ?? 'products';

            // التحقق من صحة المعاملات
            $validTables = ['products', 'categories'];
            if (!in_array($table, $validTables)) {
                $table = 'products';
            }

            // الحصول على جميع الحقول المتاحة مجمعة حسب التبويبات
            $systemFields = $this->fieldManager->getSystemFields($module, $table);
            $tabs = $this->groupFieldsByTabs($systemFields);

            // الحصول على إعدادات الشركة الحالية
            $companySettings = $this->getCompanyFieldSettings($company_id, $module, $table);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            // تحديد العنوان والروابط حسب النوع
            $entityName = ($table === 'categories') ? 'الفئات' : 'المنتجات';
            $entityUrl = ($table === 'categories') ? 'inventory/categories' : 'inventory/products';

            $data = [
                'title' => "إعدادات حقول {$entityName}",
                'tabs' => $tabs,
                'companySettings' => $companySettings,
                'company' => $company,
                'company_id' => $company_id,
                'module' => $module,
                'table' => $table,
                'entityName' => $entityName,
                'entityUrl' => $entityUrl,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => $entityName, 'url' => base_url($entityUrl)],
                    ['title' => 'إعدادات الحقول', 'active' => true]
                ]
            ];

            view('Inventory::settings/field_settings', $data);

        } catch (Exception $e) {
            flash('settings_error', 'حدث خطأ أثناء تحميل الإعدادات: ' . $e->getMessage(), 'danger');
            $fallbackUrl = ($_GET['table'] ?? 'products') === 'categories' ? 'inventory/categories' : 'inventory/products';
            redirect(base_url($fallbackUrl));
        }
    }

    // ========================================
    // الدوال الجديدة للمسارات المنفصلة
    // ========================================

    /**
     * إعدادات حقول المنتجات - مسار منفصل
     */
    public function productFields()
    {
        return $this->showFieldSettings('inventory', 'products');
    }

    /**
     * إعدادات حقول الفئات - مسار منفصل
     */
    public function categoryFields()
    {
        return $this->showFieldSettings('inventory', 'categories');
    }

    /**
     * حفظ إعدادات حقول المنتجات
     */
    public function saveProductFields()
    {
        return $this->saveFieldSettings('inventory', 'products');
    }

    /**
     * حفظ إعدادات حقول الفئات
     */
    public function saveCategoryFields()
    {
        return $this->saveFieldSettings('inventory', 'categories');
    }

    /**
     * إعادة تعيين إعدادات المنتجات
     */
    public function resetProductFields()
    {
        return $this->resetFieldSettings('inventory', 'products');
    }

    /**
     * إعادة تعيين إعدادات الفئات
     */
    public function resetCategoryFields()
    {
        return $this->resetFieldSettings('inventory', 'categories');
    }

    /**
     * معاينة إعدادات المنتجات
     */
    public function previewProductFields()
    {
        return $this->previewFieldSettings('inventory', 'products');
    }

    /**
     * معاينة إعدادات الفئات
     */
    public function previewCategoryFields()
    {
        return $this->previewFieldSettings('inventory', 'categories');
    }

    // ========================================
    // الدوال المساعدة الموحدة
    // ========================================

    /**
     * عرض صفحة إعدادات الحقول - دالة موحدة
     */
    private function showFieldSettings($module, $table)
    {
        try {
            $user = current_user();
            $company_id = $user['current_company_id'];

            // الحصول على جميع الحقول المتاحة مجمعة حسب التبويبات
            $systemFields = $this->fieldManager->getSystemFields($module, $table);
            $tabs = $this->groupFieldsByTabs($systemFields);

            // الحصول على إعدادات الشركة الحالية
            $companySettings = $this->getCompanyFieldSettings($company_id, $module, $table);

            // الحصول على معلومات الشركة
            global $db;
            $stmt = $db->prepare("SELECT CompanyName, CompanyNameEN FROM companies WHERE CompanyID = ?");
            $stmt->execute([$company_id]);
            $company = $stmt->fetch(\PDO::FETCH_ASSOC);

            // تحديد العنوان والروابط حسب النوع
            $entityName = ($table === 'categories') ? 'الفئات' : 'المنتجات';
            $entityUrl = ($table === 'categories') ? 'inventory/categories' : 'inventory/products';
            $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';

            $data = [
                'title' => "إعدادات حقول {$entityName}",
                'tabs' => $tabs,
                'companySettings' => $companySettings,
                'company' => $company,
                'company_id' => $company_id,
                'module' => $module,
                'table' => $table,
                'entityName' => $entityName,
                'entityUrl' => $entityUrl,
                'settingsUrl' => $settingsUrl,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => $entityName, 'url' => base_url($entityUrl)],
                    ['title' => 'إعدادات الحقول', 'active' => true]
                ]
            ];

            view('Inventory::settings/field_settings', $data);

        } catch (Exception $e) {
            flash('settings_error', 'حدث خطأ أثناء تحميل الإعدادات: ' . $e->getMessage(), 'danger');
            $fallbackUrl = ($table === 'categories') ? 'inventory/categories' : 'inventory/products';
            redirect(base_url($fallbackUrl));
        }
    }

    /**
     * حفظ إعدادات الحقول - دالة موحدة
     */
    private function saveFieldSettings($module, $table)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';
                redirect(base_url($settingsUrl));
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            // التحقق من البيانات
            if (empty($_POST['fields']) || !is_array($_POST['fields'])) {
                throw new Exception('يجب اختيار حقل واحد على الأقل');
            }

            global $db;
            $db->beginTransaction();

            // حذف الإعدادات السابقة للجدول المحدد
            $this->clearCompanyFieldSettings($company_id, $module, $table);

            // حفظ الإعدادات الجديدة
            foreach ($_POST['fields'] as $fieldId => $settings) {
                if (!empty($settings['is_enabled'])) {
                    $this->saveFieldSetting($company_id, $fieldId, $settings, $user_id);
                }
            }

            $db->commit();

            $entityName = ($table === 'categories') ? 'الفئات' : 'المنتجات';
            flash('settings_success', "تم حفظ إعدادات حقول {$entityName} بنجاح", 'success');

            // التوجه حسب الزر المضغوط
            $entityUrl = ($table === 'categories') ? 'inventory/categories' : 'inventory/products';
            $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';

            if (isset($_POST['save_and_continue'])) {
                redirect(base_url($entityUrl));
            } else {
                redirect(base_url($settingsUrl));
            }

        } catch (Exception $e) {
            global $db;
            $db->rollBack();
            flash('settings_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';
            redirect(base_url($settingsUrl));
        }
    }

    /**
     * إعادة تعيين إعدادات الحقول - دالة موحدة
     */
    private function resetFieldSettings($module, $table)
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';
                redirect(base_url($settingsUrl));
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            global $db;
            $db->beginTransaction();

            // حذف الإعدادات الحالية
            $this->clearCompanyFieldSettings($company_id, $module, $table);

            // تطبيق الإعدادات الافتراضية
            $this->applyDefaultSettings($company_id, $user_id, $module, $table);

            $db->commit();

            $entityName = ($table === 'categories') ? 'الفئات' : 'المنتجات';
            flash('settings_success', "تم إعادة تعيين إعدادات {$entityName} للافتراضية بنجاح", 'success');

            $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';
            redirect(base_url($settingsUrl));

        } catch (Exception $e) {
            global $db;
            $db->rollBack();
            flash('settings_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            $settingsUrl = ($table === 'categories') ? 'inventory/settings/fields_category' : 'inventory/settings/fields_product';
            redirect(base_url($settingsUrl));
        }
    }

    /**
     * معاينة إعدادات الحقول - دالة موحدة
     */
    private function previewFieldSettings($module, $table)
    {
        try {
            $company_id = current_user()['current_company_id'];

            // الحصول على الحقول المختارة
            $selectedFields = $this->fieldManager->getCompanySelectedFields($company_id, $module, $table);

            if (empty($selectedFields)) {
                echo json_encode([
                    'success' => false,
                    'message' => 'لا توجد حقول مختارة'
                ]);
                return;
            }

            // تجميع الحقول حسب التبويبات
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, $module, $table, 'form');

            echo json_encode([
                'success' => true,
                'tabs' => $tabs,
                'fieldsCount' => count($selectedFields)
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * حفظ إعدادات الحقول - الدالة القديمة للتوافق
     */
    public function save()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/settings/fields'));
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            // الحصول على معاملات module و table
            $module = $_POST['module'] ?? $_GET['module'] ?? 'inventory';
            $table = $_POST['table'] ?? $_GET['table'] ?? 'products';

            // التحقق من البيانات
            if (empty($_POST['fields']) || !is_array($_POST['fields'])) {
                throw new Exception('يجب اختيار حقل واحد على الأقل');
            }

            global $db;
            $db->beginTransaction();

            // حذف الإعدادات السابقة للجدول المحدد
            $this->clearCompanyFieldSettings($company_id, $module, $table);

            // حفظ الإعدادات الجديدة
            foreach ($_POST['fields'] as $fieldId => $settings) {
                if (!empty($settings['is_enabled'])) {
                    $this->saveFieldSetting($company_id, $fieldId, $settings, $user_id);
                }
            }

            $db->commit();

            $entityName = ($table === 'categories') ? 'الفئات' : 'المنتجات';
            flash('settings_success', "تم حفظ إعدادات حقول {$entityName} بنجاح", 'success');

            // التوجه حسب الزر المضغوط
            $entityUrl = ($table === 'categories') ? 'inventory/categories' : 'inventory/products';
            if (isset($_POST['save_and_continue'])) {
                redirect(base_url($entityUrl));
            } else {
                redirect(base_url("inventory/settings/fields?module={$module}&table={$table}"));
            }

        } catch (Exception $e) {
            global $db;
            $db->rollBack();
            flash('settings_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/settings/fields'));
        }
    }

    /**
     * إعادة تعيين الإعدادات للافتراضية
     */
    public function reset()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/settings/fields'));
            }

            $company_id = current_user()['current_company_id'];
            $user_id = current_user_id();

            global $db;
            $db->beginTransaction();

            // حذف الإعدادات الحالية
            $this->clearCompanyFieldSettings($company_id);

            // تطبيق الإعدادات الافتراضية
            $this->applyDefaultSettings($company_id, $user_id);

            $db->commit();

            flash('settings_success', 'تم إعادة تعيين الإعدادات للافتراضية بنجاح', 'success');
            redirect(base_url('inventory/settings/fields'));

        } catch (Exception $e) {
            global $db;
            $db->rollBack();
            flash('settings_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/settings/fields'));
        }
    }

    /**
     * معاينة الإعدادات
     */
    public function preview()
    {
        try {
            $company_id = current_user()['current_company_id'];

            // الحصول على الحقول المختارة
            $selectedFields = $this->fieldManager->getCompanySelectedFields($company_id, 'inventory', 'products');
            
            if (empty($selectedFields)) {
                echo json_encode([
                    'success' => false,
                    'message' => 'لا توجد حقول مختارة'
                ]);
                return;
            }

            // تجميع الحقول حسب التبويبات
            $tabs = $this->fieldManager->getCompanyFieldsGroupedByTabs($company_id, 'inventory', 'products', 'form');

            echo json_encode([
                'success' => true,
                'tabs' => $tabs,
                'fieldsCount' => count($selectedFields)
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    // ========================================
    // الدوال المساعدة
    // ========================================

    /**
     * تجميع الحقول حسب التبويبات
     */
    private function groupFieldsByTabs($fields)
    {
        $tabs = [];
        
        foreach ($fields as $field) {
            $tabId = $field['tab_id'];
            
            if (!isset($tabs[$tabId])) {
                $tabs[$tabId] = [
                    'tab_id' => $tabId,
                    'tab_name' => $field['tab_name'],
                    'tab_label_ar' => $field['tab_label_ar'],
                    'tab_label_en' => $field['tab_label_en'],
                    'tab_icon' => $field['tab_icon'],
                    'tab_color' => $field['tab_color'],
                    'tab_order' => $field['tab_order'],
                    'fields' => []
                ];
            }
            
            $tabs[$tabId]['fields'][] = $field;
        }

        // ترتيب التبويبات
        uasort($tabs, function($a, $b) {
            return $a['tab_order'] - $b['tab_order'];
        });

        return $tabs;
    }

    /**
     * الحصول على إعدادات الشركة الحالية
     */
    private function getCompanyFieldSettings($companyId, $module = 'inventory', $table = 'products')
    {
        global $db;

        $sql = "SELECT cfs.field_id, cfs.is_enabled, cfs.is_visible_form, cfs.is_visible_table, cfs.is_visible_details,
                       cfs.is_required, cfs.is_searchable, cfs.is_filterable, cfs.is_sortable, cfs.is_exportable,
                       cfs.custom_label_ar, cfs.custom_label_en, cfs.display_order_form, cfs.display_order_table
                FROM company_field_selections cfs
                INNER JOIN system_fields sf ON cfs.field_id = sf.field_id
                WHERE cfs.company_id = ? AND sf.module_name = ? AND sf.table_name = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$companyId, $module, $table]);
        $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);

        $settings = [];
        foreach ($results as $row) {
            $settings[$row['field_id']] = $row;
        }

        return $settings;
    }

    /**
     * حذف إعدادات الشركة للجدول المحدد
     */
    private function clearCompanyFieldSettings($companyId, $module = 'inventory', $table = 'products')
    {
        global $db;

        $sql = "DELETE cfs FROM company_field_selections cfs
                INNER JOIN system_fields sf ON cfs.field_id = sf.field_id
                WHERE cfs.company_id = ? AND sf.module_name = ? AND sf.table_name = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$companyId, $module, $table]);
    }

    /**
     * حفظ إعداد حقل واحد
     */
    private function saveFieldSetting($companyId, $fieldId, $settings, $userId)
    {
        global $db;
        
        $sql = "INSERT INTO company_field_selections (
                    company_id, field_id, is_enabled, is_visible_form, is_visible_table, 
                    is_visible_details, is_required, is_searchable, is_filterable, 
                    is_sortable, is_exportable, custom_label_ar, custom_label_en,
                    display_order_form, display_order_table, configured_by, configured_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $companyId,
            $fieldId,
            1, // is_enabled
            !empty($settings['is_visible_form']) ? 1 : 0,
            !empty($settings['is_visible_table']) ? 1 : 0,
            !empty($settings['is_visible_details']) ? 1 : 0,
            !empty($settings['is_required']) ? 1 : 0,
            !empty($settings['is_searchable']) ? 1 : 0,
            !empty($settings['is_filterable']) ? 1 : 0,
            !empty($settings['is_sortable']) ? 1 : 0,
            !empty($settings['is_exportable']) ? 1 : 0,
            trim($settings['custom_label_ar'] ?? ''),
            trim($settings['custom_label_en'] ?? ''),
            (int)($settings['display_order_form'] ?? 0),
            (int)($settings['display_order_table'] ?? 0),
            $userId
        ]);
    }

    /**
     * تطبيق الإعدادات الافتراضية
     */
    private function applyDefaultSettings($companyId, $userId, $module = 'inventory', $table = 'products')
    {
        global $db;

        // الحصول على الحقول الأساسية
        $sql = "SELECT field_id, is_required_default FROM system_fields
                WHERE module_name = ? AND table_name = ?
                AND (is_core_field = 1 OR is_required_default = 1)";

        $stmt = $db->prepare($sql);
        $stmt->execute([$module, $table]);
        $coreFields = $stmt->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($coreFields as $field) {
            $settings = [
                'is_visible_form' => 1,
                'is_visible_table' => 1,
                'is_visible_details' => 1,
                'is_required' => $field['is_required_default'],
                'is_searchable' => 1,
                'is_filterable' => 0,
                'is_sortable' => 1,
                'is_exportable' => 1
            ];

            $this->saveFieldSetting($companyId, $field['field_id'], $settings, $userId);
        }
    }
}
