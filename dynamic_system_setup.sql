-- ========================================
-- إعداد النظام الديناميكي للجداول
-- هذا الملف ينشئ الجداول الأساسية للنظام الديناميكي
-- بدون المساس بالنظام الحالي
-- ========================================

-- 1. جدول مكتبة الحقول المتاحة
CREATE TABLE `field_library` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_code` varchar(50) NOT NULL,
  `field_name_ar` varchar(100) NOT NULL,
  `field_name_en` varchar(100) NOT NULL,
  `mysql_type` varchar(100) NOT NULL,
  `input_type` varchar(20) NOT NULL,
  `validation_rules` json DEFAULT NULL,
  `field_options` json DEFAULT NULL,
  `default_value` varchar(255) DEFAULT NULL,
  `is_core_field` tinyint(1) DEFAULT 0,
  `is_required_default` tinyint(1) DEFAULT 0,
  `category` varchar(50) DEFAULT 'general',
  `display_order` int DEFAULT 0,
  `description_ar` text,
  `description_en` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `module_entity_field` (`module_code`, `entity_type`, `field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. جدول إعدادات حقول الشركات
CREATE TABLE `company_entity_structure` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_id` int NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `is_required` tinyint(1) DEFAULT 0,
  `is_visible` tinyint(1) DEFAULT 1,
  `is_searchable` tinyint(1) DEFAULT 1,
  `is_listable` tinyint(1) DEFAULT 1,
  `display_order` int DEFAULT 0,
  `custom_label_ar` varchar(100) DEFAULT NULL,
  `custom_label_en` varchar(100) DEFAULT NULL,
  `field_group` varchar(50) DEFAULT 'general',
  `validation_override` json DEFAULT NULL,
  `configured_by` int NOT NULL,
  `configured_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`),
  FOREIGN KEY (`field_id`) REFERENCES `field_library`(`field_id`),
  UNIQUE KEY `company_entity_field` (`company_id`, `module_code`, `entity_type`, `field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. جدول سجل الجداول الديناميكية
CREATE TABLE `dynamic_tables_registry` (
  `table_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `table_structure` json NOT NULL,
  `is_created` tinyint(1) DEFAULT 0,
  `last_modified_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_id`),
  UNIQUE KEY `company_entity` (`company_id`, `module_code`, `entity_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ========================================
-- بيانات مكتبة الحقول للمنتجات
-- ========================================

INSERT INTO `field_library` (`field_id`, `module_code`, `entity_type`, `field_code`, `field_name_ar`, `field_name_en`, `mysql_type`, `input_type`, `validation_rules`, `field_options`, `default_value`, `is_core_field`, `is_required_default`, `category`, `display_order`, `description_ar`, `description_en`) VALUES

-- الحقول الأساسية (إجبارية)
(1, 'inventory', 'product', 'product_id', 'معرف المنتج', 'Product ID', 'INT NOT NULL AUTO_INCREMENT PRIMARY KEY', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 1, 'المعرف الفريد للمنتج', 'Unique product identifier'),
(2, 'inventory', 'product', 'company_id', 'معرف الشركة', 'Company ID', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 2, 'معرف الشركة المالكة', 'Owner company identifier'),
(3, 'inventory', 'product', 'module_code', 'كود الوحدة', 'Module Code', 'VARCHAR(50) NOT NULL DEFAULT \'inventory\'', 'hidden', NULL, NULL, 'inventory', 1, 0, 'core', 3, 'كود الوحدة', 'Module code'),
(4, 'inventory', 'product', 'product_code', 'كود المنتج', 'Product Code', 'VARCHAR(50) NOT NULL', 'text', '{"required": true, "unique": true, "max_length": 50}', NULL, NULL, 1, 1, 'basic', 4, 'كود المنتج الفريد', 'Unique product code'),
(5, 'inventory', 'product', 'product_name_ar', 'اسم المنتج', 'Product Name', 'VARCHAR(200) NOT NULL', 'text', '{"required": true, "max_length": 200}', NULL, NULL, 1, 1, 'basic', 5, 'اسم المنتج باللغة العربية', 'Product name in Arabic'),

-- الحقول الاختيارية الأساسية
(6, 'inventory', 'product', 'product_name_en', 'الاسم بالإنجليزية', 'English Name', 'VARCHAR(200) DEFAULT NULL', 'text', '{"max_length": 200}', NULL, NULL, 0, 0, 'basic', 6, 'اسم المنتج بالإنجليزية', 'Product name in English'),
(7, 'inventory', 'product', 'barcode', 'الباركود', 'Barcode', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'basic', 7, 'رمز الباركود', 'Barcode number'),
(8, 'inventory', 'product', 'description_ar', 'الوصف', 'Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 8, 'وصف المنتج بالعربية', 'Product description in Arabic'),
(9, 'inventory', 'product', 'description_en', 'الوصف بالإنجليزية', 'English Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 9, 'وصف المنتج بالإنجليزية', 'Product description in English'),

-- حقول التصنيف
(10, 'inventory', 'product', 'category_id', 'الفئة', 'Category', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_categories", "display": "category_name_ar", "value": "category_id"}', NULL, 0, 0, 'classification', 10, 'فئة المنتج', 'Product category'),
(11, 'inventory', 'product', 'unit_id', 'وحدة القياس', 'Unit', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_units", "display": "unit_name_ar", "value": "unit_id"}', NULL, 0, 0, 'classification', 11, 'وحدة قياس المنتج', 'Product unit of measure'),
(12, 'inventory', 'product', 'product_type', 'نوع المنتج', 'Product Type', 'ENUM(\'product\',\'service\',\'digital\') DEFAULT \'product\'', 'select', NULL, '["product", "service", "digital"]', 'product', 0, 0, 'classification', 12, 'نوع المنتج', 'Product type'),

-- حقول الأسعار
(13, 'inventory', 'product', 'cost_price', 'سعر التكلفة', 'Cost Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 13, 'سعر تكلفة المنتج', 'Product cost price'),
(14, 'inventory', 'product', 'selling_price', 'سعر البيع', 'Selling Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 14, 'سعر بيع المنتج', 'Product selling price'),

-- حقول المخزون
(15, 'inventory', 'product', 'track_inventory', 'تتبع المخزون', 'Track Inventory', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 0, 0, 'inventory', 15, 'هل يتم تتبع مخزون المنتج', 'Track product inventory'),
(16, 'inventory', 'product', 'min_stock_level', 'الحد الأدنى للمخزون', 'Min Stock Level', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 16, 'الحد الأدنى للمخزون', 'Minimum stock level'),
(17, 'inventory', 'product', 'max_stock_level', 'الحد الأقصى للمخزون', 'Max Stock Level', 'DECIMAL(15,2) DEFAULT NULL', 'number', '{"min": 0}', NULL, NULL, 0, 0, 'inventory', 17, 'الحد الأقصى للمخزون', 'Maximum stock level'),
(18, 'inventory', 'product', 'reorder_point', 'نقطة إعادة الطلب', 'Reorder Point', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 18, 'نقطة إعادة طلب المنتج', 'Product reorder point'),

-- الخصائص الفيزيائية
(19, 'inventory', 'product', 'weight', 'الوزن', 'Weight', 'DECIMAL(10,3) DEFAULT NULL', 'number', '{"min": 0, "step": 0.001}', NULL, NULL, 0, 0, 'physical', 19, 'وزن المنتج', 'Product weight'),
(20, 'inventory', 'product', 'dimensions', 'الأبعاد', 'Dimensions', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 20, 'أبعاد المنتج', 'Product dimensions'),

-- حقول إضافية متقدمة
(21, 'inventory', 'product', 'color', 'اللون', 'Color', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["أحمر", "أزرق", "أخضر", "أصفر", "أسود", "أبيض", "بني", "رمادي", "بنفسجي", "برتقالي"]', NULL, 0, 0, 'physical', 21, 'لون المنتج', 'Product color'),
(22, 'inventory', 'product', 'size', 'الحجم', 'Size', 'VARCHAR(20) DEFAULT NULL', 'select', NULL, '["XS", "S", "M", "L", "XL", "XXL", "صغير", "متوسط", "كبير", "كبير<|im_start|>"]', NULL, 0, 0, 'physical', 22, 'حجم المنتج', 'Product size'),
(23, 'inventory', 'product', 'material', 'المادة', 'Material', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 23, 'مادة صنع المنتج', 'Product material'),
(24, 'inventory', 'product', 'warranty_period', 'فترة الضمان', 'Warranty Period', 'INT DEFAULT NULL', 'number', '{"min": 0, "max": 120}', NULL, NULL, 0, 0, 'warranty', 24, 'فترة الضمان بالشهور', 'Warranty period in months'),
(25, 'inventory', 'product', 'expiry_date', 'تاريخ الانتهاء', 'Expiry Date', 'DATE DEFAULT NULL', 'date', NULL, NULL, NULL, 0, 0, 'dates', 25, 'تاريخ انتهاء صلاحية المنتج', 'Product expiry date'),
(26, 'inventory', 'product', 'supplier_code', 'كود المورد', 'Supplier Code', 'VARCHAR(50) DEFAULT NULL', 'text', '{"max_length": 50}', NULL, NULL, 0, 0, 'supplier', 26, 'كود المنتج عند المورد', 'Product code at supplier'),
(27, 'inventory', 'product', 'origin_country', 'بلد المنشأ', 'Origin Country', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["السعودية", "الإمارات", "مصر", "الأردن", "الكويت", "قطر", "البحرين", "عمان", "الصين", "ألمانيا", "أمريكا", "اليابان"]', NULL, 0, 0, 'supplier', 27, 'بلد منشأ المنتج', 'Product origin country'),
(28, 'inventory', 'product', 'is_fragile', 'قابل للكسر', 'Fragile', 'TINYINT(1) DEFAULT 0', 'checkbox', NULL, NULL, '0', 0, 0, 'shipping', 28, 'هل المنتج قابل للكسر', 'Is product fragile'),
(29, 'inventory', 'product', 'image_url', 'صورة المنتج', 'Product Image', 'VARCHAR(255) DEFAULT NULL', 'file', '{"accept": "image/*"}', NULL, NULL, 0, 0, 'media', 29, 'رابط صورة المنتج', 'Product image URL'),
(30, 'inventory', 'product', 'tax_rate', 'معدل الضريبة', 'Tax Rate', 'DECIMAL(5,2) DEFAULT 0.00', 'number', '{"min": 0, "max": 100, "step": 0.01}', NULL, '0.00', 0, 0, 'tax', 30, 'معدل ضريبة المنتج', 'Product tax rate'),

-- حقول النظام (إجبارية)
(31, 'inventory', 'product', 'is_active', 'نشط', 'Active', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 1, 0, 'core', 31, 'حالة نشاط المنتج', 'Product active status'),
(32, 'inventory', 'product', 'created_by', 'أنشئ بواسطة', 'Created By', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 32, 'المستخدم الذي أنشأ المنتج', 'User who created the product'),
(33, 'inventory', 'product', 'updated_by', 'حُدث بواسطة', 'Updated By', 'INT DEFAULT NULL', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 33, 'المستخدم الذي حدث المنتج', 'User who updated the product'),
(34, 'inventory', 'product', 'created_at', 'تاريخ الإنشاء', 'Created At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP', 1, 0, 'core', 34, 'تاريخ إنشاء المنتج', 'Product creation date'),
(35, 'inventory', 'product', 'updated_at', 'تاريخ التحديث', 'Updated At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 1, 0, 'core', 35, 'تاريخ آخر تحديث للمنتج', 'Product last update date');
