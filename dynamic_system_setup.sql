-- ========================================
-- إعداد النظام الديناميكي الموحد
-- جداول ديناميكية موحدة لجميع الشركات
-- يتم التمييز بين الشركات من خلال company_id
-- ========================================

-- 1. جدول مكتبة الحقول المتاحة
CREATE TABLE `field_library` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_code` varchar(50) NOT NULL,
  `field_name_ar` varchar(100) NOT NULL,
  `field_name_en` varchar(100) NOT NULL,
  `mysql_type` varchar(100) NOT NULL,
  `input_type` varchar(20) NOT NULL,
  `validation_rules` json DEFAULT NULL,
  `field_options` json DEFAULT NULL,
  `default_value` varchar(255) DEFAULT NULL,
  `is_core_field` tinyint(1) DEFAULT 0,
  `is_required_default` tinyint(1) DEFAULT 0,
  `category` varchar(50) DEFAULT 'general',
  `display_order` int DEFAULT 0,
  `description_ar` text,
  `description_en` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `module_entity_field` (`module_code`, `entity_type`, `field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. جدول إعدادات حقول الشركات
CREATE TABLE `company_entity_structure` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_id` int NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `is_required` tinyint(1) DEFAULT 0,
  `is_visible` tinyint(1) DEFAULT 1,
  `is_searchable` tinyint(1) DEFAULT 1,
  `is_listable` tinyint(1) DEFAULT 1,
  `display_order` int DEFAULT 0,
  `custom_label_ar` varchar(100) DEFAULT NULL,
  `custom_label_en` varchar(100) DEFAULT NULL,
  `field_group` varchar(50) DEFAULT 'general',
  `validation_override` json DEFAULT NULL,
  `configured_by` int NOT NULL,
  `configured_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`),
  FOREIGN KEY (`field_id`) REFERENCES `field_library`(`field_id`),
  UNIQUE KEY `company_entity_field` (`company_id`, `module_code`, `entity_type`, `field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. الجدول الديناميكي الموحد للمنتجات
CREATE TABLE `dynamic_inventory_products` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `entity_data` json NOT NULL,
  `search_text` text GENERATED ALWAYS AS (
    CONCAT(
      IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.product_code')), ''), ' ',
      IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.product_name_ar')), ''), ' ',
      IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.product_name_en')), ''), ' ',
      IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.barcode')), ''), ' ',
      IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.description_ar')), '')
    )
  ) STORED,
  `is_active` tinyint(1) GENERATED ALWAYS AS (
    IFNULL(JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.is_active')), 1)
  ) STORED,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_product_code` (`company_id`, `module_code`, `entity_type`, (JSON_UNQUOTE(JSON_EXTRACT(entity_data, '$.product_code')))),
  KEY `idx_company_module` (`company_id`, `module_code`, `entity_type`),
  KEY `idx_search_text` (`search_text`(255)),
  KEY `idx_active` (`company_id`, `is_active`),
  KEY `idx_created_at` (`created_at`),
  FULLTEXT KEY `ft_search_text` (`search_text`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ========================================
-- بيانات مكتبة الحقول للمنتجات
-- ========================================

INSERT INTO `field_library` (`field_id`, `module_code`, `entity_type`, `field_code`, `field_name_ar`, `field_name_en`, `mysql_type`, `input_type`, `validation_rules`, `field_options`, `default_value`, `is_core_field`, `is_required_default`, `category`, `display_order`, `description_ar`, `description_en`) VALUES

-- الحقول الأساسية (إجبارية)
(1, 'inventory', 'product', 'product_id', 'معرف المنتج', 'Product ID', 'INT NOT NULL AUTO_INCREMENT PRIMARY KEY', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 1, 'المعرف الفريد للمنتج', 'Unique product identifier'),
(2, 'inventory', 'product', 'company_id', 'معرف الشركة', 'Company ID', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 2, 'معرف الشركة المالكة', 'Owner company identifier'),
(3, 'inventory', 'product', 'module_code', 'كود الوحدة', 'Module Code', 'VARCHAR(50) NOT NULL DEFAULT \'inventory\'', 'hidden', NULL, NULL, 'inventory', 1, 0, 'core', 3, 'كود الوحدة', 'Module code'),
(4, 'inventory', 'product', 'product_code', 'كود المنتج', 'Product Code', 'VARCHAR(50) NOT NULL', 'text', '{"required": true, "unique": true, "max_length": 50}', NULL, NULL, 1, 1, 'basic', 4, 'كود المنتج الفريد', 'Unique product code'),
(5, 'inventory', 'product', 'product_name_ar', 'اسم المنتج', 'Product Name', 'VARCHAR(200) NOT NULL', 'text', '{"required": true, "max_length": 200}', NULL, NULL, 1, 1, 'basic', 5, 'اسم المنتج باللغة العربية', 'Product name in Arabic'),

-- الحقول الاختيارية الأساسية
(6, 'inventory', 'product', 'product_name_en', 'الاسم بالإنجليزية', 'English Name', 'VARCHAR(200) DEFAULT NULL', 'text', '{"max_length": 200}', NULL, NULL, 0, 0, 'basic', 6, 'اسم المنتج بالإنجليزية', 'Product name in English'),
(7, 'inventory', 'product', 'barcode', 'الباركود', 'Barcode', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'basic', 7, 'رمز الباركود', 'Barcode number'),
(8, 'inventory', 'product', 'description_ar', 'الوصف', 'Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 8, 'وصف المنتج بالعربية', 'Product description in Arabic'),
(9, 'inventory', 'product', 'description_en', 'الوصف بالإنجليزية', 'English Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 9, 'وصف المنتج بالإنجليزية', 'Product description in English'),

-- حقول التصنيف
(10, 'inventory', 'product', 'category_id', 'الفئة', 'Category', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_categories", "display": "category_name_ar", "value": "category_id"}', NULL, 0, 0, 'classification', 10, 'فئة المنتج', 'Product category'),
(11, 'inventory', 'product', 'unit_id', 'وحدة القياس', 'Unit', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_units", "display": "unit_name_ar", "value": "unit_id"}', NULL, 0, 0, 'classification', 11, 'وحدة قياس المنتج', 'Product unit of measure'),
(12, 'inventory', 'product', 'product_type', 'نوع المنتج', 'Product Type', 'ENUM(\'product\',\'service\',\'digital\') DEFAULT \'product\'', 'select', NULL, '["product", "service", "digital"]', 'product', 0, 0, 'classification', 12, 'نوع المنتج', 'Product type'),

-- حقول الأسعار
(13, 'inventory', 'product', 'cost_price', 'سعر التكلفة', 'Cost Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 13, 'سعر تكلفة المنتج', 'Product cost price'),
(14, 'inventory', 'product', 'selling_price', 'سعر البيع', 'Selling Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 14, 'سعر بيع المنتج', 'Product selling price'),

-- حقول المخزون
(15, 'inventory', 'product', 'track_inventory', 'تتبع المخزون', 'Track Inventory', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 0, 0, 'inventory', 15, 'هل يتم تتبع مخزون المنتج', 'Track product inventory'),
(16, 'inventory', 'product', 'min_stock_level', 'الحد الأدنى للمخزون', 'Min Stock Level', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 16, 'الحد الأدنى للمخزون', 'Minimum stock level'),
(17, 'inventory', 'product', 'max_stock_level', 'الحد الأقصى للمخزون', 'Max Stock Level', 'DECIMAL(15,2) DEFAULT NULL', 'number', '{"min": 0}', NULL, NULL, 0, 0, 'inventory', 17, 'الحد الأقصى للمخزون', 'Maximum stock level'),
(18, 'inventory', 'product', 'reorder_point', 'نقطة إعادة الطلب', 'Reorder Point', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 18, 'نقطة إعادة طلب المنتج', 'Product reorder point'),

-- الخصائص الفيزيائية
(19, 'inventory', 'product', 'weight', 'الوزن', 'Weight', 'DECIMAL(10,3) DEFAULT NULL', 'number', '{"min": 0, "step": 0.001}', NULL, NULL, 0, 0, 'physical', 19, 'وزن المنتج', 'Product weight'),
(20, 'inventory', 'product', 'dimensions', 'الأبعاد', 'Dimensions', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 20, 'أبعاد المنتج', 'Product dimensions'),

-- حقول إضافية متقدمة
(21, 'inventory', 'product', 'color', 'اللون', 'Color', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["أحمر", "أزرق", "أخضر", "أصفر", "أسود", "أبيض", "بني", "رمادي", "بنفسجي", "برتقالي"]', NULL, 0, 0, 'physical', 21, 'لون المنتج', 'Product color'),
(22, 'inventory', 'product', 'size', 'الحجم', 'Size', 'VARCHAR(20) DEFAULT NULL', 'select', NULL, '["XS", "S", "M", "L", "XL", "XXL", "صغير", "متوسط", "كبير", "كبير<|im_start|>"]', NULL, 0, 0, 'physical', 22, 'حجم المنتج', 'Product size'),
(23, 'inventory', 'product', 'material', 'المادة', 'Material', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 23, 'مادة صنع المنتج', 'Product material'),
(24, 'inventory', 'product', 'warranty_period', 'فترة الضمان', 'Warranty Period', 'INT DEFAULT NULL', 'number', '{"min": 0, "max": 120}', NULL, NULL, 0, 0, 'warranty', 24, 'فترة الضمان بالشهور', 'Warranty period in months'),
(25, 'inventory', 'product', 'expiry_date', 'تاريخ الانتهاء', 'Expiry Date', 'DATE DEFAULT NULL', 'date', NULL, NULL, NULL, 0, 0, 'dates', 25, 'تاريخ انتهاء صلاحية المنتج', 'Product expiry date'),
(26, 'inventory', 'product', 'supplier_code', 'كود المورد', 'Supplier Code', 'VARCHAR(50) DEFAULT NULL', 'text', '{"max_length": 50}', NULL, NULL, 0, 0, 'supplier', 26, 'كود المنتج عند المورد', 'Product code at supplier'),
(27, 'inventory', 'product', 'origin_country', 'بلد المنشأ', 'Origin Country', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["السعودية", "الإمارات", "مصر", "الأردن", "الكويت", "قطر", "البحرين", "عمان", "الصين", "ألمانيا", "أمريكا", "اليابان"]', NULL, 0, 0, 'supplier', 27, 'بلد منشأ المنتج', 'Product origin country'),
(28, 'inventory', 'product', 'is_fragile', 'قابل للكسر', 'Fragile', 'TINYINT(1) DEFAULT 0', 'checkbox', NULL, NULL, '0', 0, 0, 'shipping', 28, 'هل المنتج قابل للكسر', 'Is product fragile'),
(29, 'inventory', 'product', 'image_url', 'صورة المنتج', 'Product Image', 'VARCHAR(255) DEFAULT NULL', 'file', '{"accept": "image/*"}', NULL, NULL, 0, 0, 'media', 29, 'رابط صورة المنتج', 'Product image URL'),
(30, 'inventory', 'product', 'tax_rate', 'معدل الضريبة', 'Tax Rate', 'DECIMAL(5,2) DEFAULT 0.00', 'number', '{"min": 0, "max": 100, "step": 0.01}', NULL, '0.00', 0, 0, 'tax', 30, 'معدل ضريبة المنتج', 'Product tax rate'),

-- حقول النظام (إجبارية)
(31, 'inventory', 'product', 'is_active', 'نشط', 'Active', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 1, 0, 'core', 31, 'حالة نشاط المنتج', 'Product active status'),
(32, 'inventory', 'product', 'created_by', 'أنشئ بواسطة', 'Created By', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 32, 'المستخدم الذي أنشأ المنتج', 'User who created the product'),
(33, 'inventory', 'product', 'updated_by', 'حُدث بواسطة', 'Updated By', 'INT DEFAULT NULL', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 33, 'المستخدم الذي حدث المنتج', 'User who updated the product'),
(34, 'inventory', 'product', 'created_at', 'تاريخ الإنشاء', 'Created At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP', 1, 0, 'core', 34, 'تاريخ إنشاء المنتج', 'Product creation date'),
(35, 'inventory', 'product', 'updated_at', 'تاريخ التحديث', 'Updated At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 1, 0, 'core', 35, 'تاريخ آخر تحديث للمنتج', 'Product last update date');

-- ========================================
-- بيانات افتراضية للشركة رقم 4
-- ========================================

-- إعدادات حقول الشركة رقم 4 (مثال شامل)
INSERT INTO `company_entity_structure` (`company_id`, `module_code`, `entity_type`, `field_id`, `is_enabled`, `is_required`, `is_visible`, `is_searchable`, `is_listable`, `display_order`, `custom_label_ar`, `custom_label_en`, `field_group`, `configured_by`) VALUES

-- الحقول الأساسية (إجبارية)
(4, 'inventory', 'product', 1, 1, 0, 0, 0, 0, 1, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 2, 1, 1, 0, 0, 0, 2, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 3, 1, 0, 0, 0, 0, 3, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 4, 1, 1, 1, 1, 1, 4, 'كود المنتج', 'Product Code', 'basic', 35),
(4, 'inventory', 'product', 5, 1, 1, 1, 1, 1, 5, 'اسم المنتج', 'Product Name', 'basic', 35),

-- الحقول الاختيارية المفعلة
(4, 'inventory', 'product', 6, 1, 0, 1, 1, 1, 6, 'الاسم بالإنجليزية', 'English Name', 'basic', 35),
(4, 'inventory', 'product', 7, 1, 0, 1, 1, 1, 7, 'الباركود', 'Barcode', 'basic', 35),
(4, 'inventory', 'product', 8, 1, 0, 1, 1, 0, 8, 'الوصف', 'Description', 'basic', 35),
(4, 'inventory', 'product', 10, 1, 1, 1, 1, 1, 10, 'الفئة', 'Category', 'classification', 35),
(4, 'inventory', 'product', 11, 1, 1, 1, 1, 1, 11, 'وحدة القياس', 'Unit', 'classification', 35),
(4, 'inventory', 'product', 12, 1, 0, 1, 1, 1, 12, 'نوع المنتج', 'Product Type', 'classification', 35),

-- حقول الأسعار
(4, 'inventory', 'product', 13, 1, 1, 1, 0, 1, 13, 'سعر التكلفة', 'Cost Price', 'pricing', 35),
(4, 'inventory', 'product', 14, 1, 1, 1, 0, 1, 14, 'سعر البيع', 'Selling Price', 'pricing', 35),

-- حقول المخزون
(4, 'inventory', 'product', 15, 1, 0, 1, 0, 0, 15, 'تتبع المخزون', 'Track Inventory', 'inventory', 35),
(4, 'inventory', 'product', 16, 1, 0, 1, 0, 0, 16, 'الحد الأدنى للمخزون', 'Min Stock Level', 'inventory', 35),
(4, 'inventory', 'product', 18, 1, 0, 1, 0, 0, 18, 'نقطة إعادة الطلب', 'Reorder Point', 'inventory', 35),

-- الخصائص الفيزيائية
(4, 'inventory', 'product', 19, 1, 0, 1, 0, 1, 19, 'الوزن (كجم)', 'Weight (KG)', 'physical', 35),
(4, 'inventory', 'product', 21, 1, 0, 1, 1, 1, 21, 'اللون', 'Color', 'physical', 35),
(4, 'inventory', 'product', 22, 1, 0, 1, 1, 1, 22, 'الحجم', 'Size', 'physical', 35),

-- حقول متقدمة
(4, 'inventory', 'product', 24, 1, 0, 1, 0, 0, 24, 'فترة الضمان (شهر)', 'Warranty (Months)', 'warranty', 35),
(4, 'inventory', 'product', 27, 1, 0, 1, 1, 1, 27, 'بلد المنشأ', 'Origin Country', 'supplier', 35),
(4, 'inventory', 'product', 29, 1, 0, 1, 0, 0, 29, 'صورة المنتج', 'Product Image', 'media', 35),

-- حقول النظام (إجبارية)
(4, 'inventory', 'product', 31, 1, 0, 1, 0, 1, 31, 'نشط', 'Active', 'core', 35),
(4, 'inventory', 'product', 32, 1, 1, 0, 0, 0, 32, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 33, 1, 0, 0, 0, 0, 33, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 34, 1, 0, 0, 0, 0, 34, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 35, 1, 0, 0, 0, 0, 35, NULL, NULL, 'core', 35);



-- ========================================
-- بيانات تجريبية للمنتجات - الشركة رقم 4
-- ========================================

-- إدراج فئات المنتجات (إذا لم تكن موجودة)
INSERT IGNORE INTO `inventory_categories` (`category_id`, `company_id`, `category_name_ar`, `category_name_en`, `description_ar`, `is_active`, `created_by`, `created_at`) VALUES
(1, 4, 'إلكترونيات', 'Electronics', 'الأجهزة الإلكترونية والكهربائية', 1, 35, NOW()),
(2, 4, 'ملابس', 'Clothing', 'الملابس والأزياء', 1, 35, NOW()),
(3, 4, 'أثاث', 'Furniture', 'الأثاث والديكور', 1, 35, NOW()),
(4, 4, 'كتب', 'Books', 'الكتب والمطبوعات', 1, 35, NOW()),
(5, 4, 'رياضة', 'Sports', 'المعدات الرياضية', 1, 35, NOW());

-- إدراج وحدات القياس (إذا لم تكن موجودة)
INSERT IGNORE INTO `inventory_units` (`unit_id`, `company_id`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_active`, `created_by`, `created_at`) VALUES
(1, 4, 'قطعة', 'Piece', 'قطعة', 'pcs', 1, 35, NOW()),
(2, 4, 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, 35, NOW()),
(3, 4, 'متر', 'Meter', 'م', 'm', 1, 35, NOW()),
(4, 4, 'لتر', 'Liter', 'لتر', 'L', 1, 35, NOW()),
(5, 4, 'علبة', 'Box', 'علبة', 'box', 1, 35, NOW());

-- إدراج منتجات تجريبية في الجدول الديناميكي الموحد
INSERT INTO `dynamic_inventory_products` (`company_id`, `module_code`, `entity_type`, `entity_data`, `created_by`) VALUES

-- منتجات إلكترونية للشركة 4
(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'ELEC001',
  'product_name_ar', 'هاتف ذكي سامسونج جالاكسي',
  'product_name_en', 'Samsung Galaxy Smartphone',
  'barcode', '8801234567890',
  'description_ar', 'هاتف ذكي بشاشة 6.5 بوصة وذاكرة 128 جيجا',
  'category_id', 1,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 1200.00,
  'selling_price', 1500.00,
  'track_inventory', 1,
  'min_stock_level', 5.00,
  'reorder_point', 10.00,
  'weight', 0.180,
  'color', 'أسود',
  'size', 'M',
  'warranty_period', 24,
  'origin_country', 'كوريا الجنوبية',
  'is_active', 1
), 35),

(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'ELEC002',
  'product_name_ar', 'لابتوب ديل انسبايرون',
  'product_name_en', 'Dell Inspiron Laptop',
  'barcode', '8801234567891',
  'description_ar', 'لابتوب بمعالج Intel Core i5 وذاكرة 8 جيجا',
  'category_id', 1,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 2500.00,
  'selling_price', 3200.00,
  'track_inventory', 1,
  'min_stock_level', 2.00,
  'reorder_point', 5.00,
  'weight', 2.100,
  'color', 'فضي',
  'size', 'L',
  'warranty_period', 12,
  'origin_country', 'الصين',
  'is_active', 1
), 35),

(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'ELEC003',
  'product_name_ar', 'سماعات بلوتوث',
  'product_name_en', 'Bluetooth Headphones',
  'barcode', '8801234567892',
  'description_ar', 'سماعات لاسلكية عالية الجودة',
  'category_id', 1,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 150.00,
  'selling_price', 250.00,
  'track_inventory', 1,
  'min_stock_level', 10.00,
  'reorder_point', 20.00,
  'weight', 0.300,
  'color', 'أبيض',
  'size', 'S',
  'warranty_period', 6,
  'origin_country', 'الصين',
  'is_active', 1
), 35),

-- منتجات ملابس
(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'CLTH001',
  'product_name_ar', 'قميص قطني رجالي',
  'product_name_en', 'Men Cotton Shirt',
  'barcode', '8801234567893',
  'description_ar', 'قميص قطني عالي الجودة للرجال',
  'category_id', 2,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 45.00,
  'selling_price', 85.00,
  'track_inventory', 1,
  'min_stock_level', 20.00,
  'reorder_point', 50.00,
  'weight', 0.250,
  'color', 'أزرق',
  'size', 'L',
  'warranty_period', 0,
  'origin_country', 'مصر',
  'is_active', 1
), 35),

(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'CLTH002',
  'product_name_ar', 'فستان نسائي',
  'product_name_en', 'Women Dress',
  'barcode', '8801234567894',
  'description_ar', 'فستان أنيق للمناسبات',
  'category_id', 2,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 120.00,
  'selling_price', 200.00,
  'track_inventory', 1,
  'min_stock_level', 15.00,
  'reorder_point', 30.00,
  'weight', 0.400,
  'color', 'أحمر',
  'size', 'M',
  'warranty_period', 0,
  'origin_country', 'تركيا',
  'is_active', 1
), 35),

(4, 'inventory', 'product', JSON_OBJECT(
  'product_code', 'CLTH003',
  'product_name_ar', 'حذاء رياضي',
  'product_name_en', 'Sports Shoes',
  'barcode', '8801234567895',
  'description_ar', 'حذاء رياضي مريح للجري',
  'category_id', 2,
  'unit_id', 1,
  'product_type', 'product',
  'cost_price', 180.00,
  'selling_price', 280.00,
  'track_inventory', 1,
  'min_stock_level', 12.00,
  'reorder_point', 25.00,
  'weight', 0.800,
  'color', 'أسود',
  'size', '42',
  'warranty_period', 6,
  'origin_country', 'فيتنام',
  'is_active', 1
), 35);

-- ========================================
-- ملخص البيانات المدرجة للشركة رقم 4
-- ========================================

/*
تم إنشاء النظام الديناميكي الموحد بالكامل:

✅ الجداول الأساسية:
   - field_library: 35 حقل متاح للمنتجات
   - company_entity_structure: إعدادات 22 حقل مفعل للشركة 4
   - dynamic_inventory_products: جدول موحد لجميع الشركات

✅ النهج الجديد - جدول موحد ديناميكي:
   - جدول واحد لجميع الشركات: dynamic_inventory_products
   - التمييز بين الشركات عبر company_id
   - البيانات مخزنة في JSON: entity_data
   - فهارس محسنة للأداء والبحث
   - حقول محسوبة تلقائياً: search_text, is_active

✅ مزايا النهج الجديد:
   - لا حاجة لإنشاء جداول منفصلة لكل شركة
   - مرونة كاملة في إضافة حقول جديدة
   - سهولة الصيانة والتطوير
   - أداء محسن مع الفهارس المناسبة
   - دعم البحث النصي الكامل

✅ البيانات التجريبية للشركة 4:
   - 5 فئات منتجات (إلكترونيات، ملابس، أثاث، كتب، رياضة)
   - 5 وحدات قياس (قطعة، كجم، متر، لتر، علبة)
   - 6 منتجات تجريبية متنوعة في JSON

✅ الحقول المفعلة للشركة 4:
   الأساسية: كود المنتج، اسم المنتج، الاسم بالإنجليزية، الباركود، الوصف
   التصنيف: الفئة، وحدة القياس، نوع المنتج
   الأسعار: سعر التكلفة، سعر البيع
   المخزون: تتبع المخزون، الحد الأدنى، نقطة إعادة الطلب
   الفيزيائية: الوزن، اللون، الحجم
   متقدمة: فترة الضمان، بلد المنشأ، صورة المنتج
   النظام: نشط، تواريخ الإنشاء والتحديث

✅ المستخدم المكون: رقم 35
✅ تاريخ الإعداد: الحالي (NOW())

للاستخدام:
1. تشغيل هذا الملف في قاعدة البيانات
2. تحديث DynamicTableManager للعمل مع الجدول الموحد
3. الدخول إلى /inventory/dynamic/products
4. سيظهر النظام الديناميكي جاهز للاستخدام
*/

-- ========================================
-- انتهاء ملف إعداد النظام الديناميكي
-- ========================================
