-- ========================================
-- إعداد النظام الديناميكي للجداول
-- هذا الملف ينشئ الجداول الأساسية للنظام الديناميكي
-- بدون المساس بالنظام الحالي
-- ========================================

-- 1. جدول مكتبة الحقول المتاحة
CREATE TABLE `field_library` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_code` varchar(50) NOT NULL,
  `field_name_ar` varchar(100) NOT NULL,
  `field_name_en` varchar(100) NOT NULL,
  `mysql_type` varchar(100) NOT NULL,
  `input_type` varchar(20) NOT NULL,
  `validation_rules` json DEFAULT NULL,
  `field_options` json DEFAULT NULL,
  `default_value` varchar(255) DEFAULT NULL,
  `is_core_field` tinyint(1) DEFAULT 0,
  `is_required_default` tinyint(1) DEFAULT 0,
  `category` varchar(50) DEFAULT 'general',
  `display_order` int DEFAULT 0,
  `description_ar` text,
  `description_en` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `module_entity_field` (`module_code`, `entity_type`, `field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2. جدول إعدادات حقول الشركات
CREATE TABLE `company_entity_structure` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `entity_type` varchar(50) NOT NULL DEFAULT 'product',
  `field_id` int NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `is_required` tinyint(1) DEFAULT 0,
  `is_visible` tinyint(1) DEFAULT 1,
  `is_searchable` tinyint(1) DEFAULT 1,
  `is_listable` tinyint(1) DEFAULT 1,
  `display_order` int DEFAULT 0,
  `custom_label_ar` varchar(100) DEFAULT NULL,
  `custom_label_en` varchar(100) DEFAULT NULL,
  `field_group` varchar(50) DEFAULT 'general',
  `validation_override` json DEFAULT NULL,
  `configured_by` int NOT NULL,
  `configured_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`),
  FOREIGN KEY (`field_id`) REFERENCES `field_library`(`field_id`),
  UNIQUE KEY `company_entity_field` (`company_id`, `module_code`, `entity_type`, `field_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. جدول سجل الجداول الديناميكية
CREATE TABLE `dynamic_tables_registry` (
  `table_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `table_structure` json NOT NULL,
  `is_created` tinyint(1) DEFAULT 0,
  `last_modified_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_id`),
  UNIQUE KEY `company_entity` (`company_id`, `module_code`, `entity_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ========================================
-- بيانات مكتبة الحقول للمنتجات
-- ========================================

INSERT INTO `field_library` (`field_id`, `module_code`, `entity_type`, `field_code`, `field_name_ar`, `field_name_en`, `mysql_type`, `input_type`, `validation_rules`, `field_options`, `default_value`, `is_core_field`, `is_required_default`, `category`, `display_order`, `description_ar`, `description_en`) VALUES

-- الحقول الأساسية (إجبارية)
(1, 'inventory', 'product', 'product_id', 'معرف المنتج', 'Product ID', 'INT NOT NULL AUTO_INCREMENT PRIMARY KEY', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 1, 'المعرف الفريد للمنتج', 'Unique product identifier'),
(2, 'inventory', 'product', 'company_id', 'معرف الشركة', 'Company ID', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 2, 'معرف الشركة المالكة', 'Owner company identifier'),
(3, 'inventory', 'product', 'module_code', 'كود الوحدة', 'Module Code', 'VARCHAR(50) NOT NULL DEFAULT \'inventory\'', 'hidden', NULL, NULL, 'inventory', 1, 0, 'core', 3, 'كود الوحدة', 'Module code'),
(4, 'inventory', 'product', 'product_code', 'كود المنتج', 'Product Code', 'VARCHAR(50) NOT NULL', 'text', '{"required": true, "unique": true, "max_length": 50}', NULL, NULL, 1, 1, 'basic', 4, 'كود المنتج الفريد', 'Unique product code'),
(5, 'inventory', 'product', 'product_name_ar', 'اسم المنتج', 'Product Name', 'VARCHAR(200) NOT NULL', 'text', '{"required": true, "max_length": 200}', NULL, NULL, 1, 1, 'basic', 5, 'اسم المنتج باللغة العربية', 'Product name in Arabic'),

-- الحقول الاختيارية الأساسية
(6, 'inventory', 'product', 'product_name_en', 'الاسم بالإنجليزية', 'English Name', 'VARCHAR(200) DEFAULT NULL', 'text', '{"max_length": 200}', NULL, NULL, 0, 0, 'basic', 6, 'اسم المنتج بالإنجليزية', 'Product name in English'),
(7, 'inventory', 'product', 'barcode', 'الباركود', 'Barcode', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'basic', 7, 'رمز الباركود', 'Barcode number'),
(8, 'inventory', 'product', 'description_ar', 'الوصف', 'Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 8, 'وصف المنتج بالعربية', 'Product description in Arabic'),
(9, 'inventory', 'product', 'description_en', 'الوصف بالإنجليزية', 'English Description', 'TEXT DEFAULT NULL', 'textarea', NULL, NULL, NULL, 0, 0, 'basic', 9, 'وصف المنتج بالإنجليزية', 'Product description in English'),

-- حقول التصنيف
(10, 'inventory', 'product', 'category_id', 'الفئة', 'Category', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_categories", "display": "category_name_ar", "value": "category_id"}', NULL, 0, 0, 'classification', 10, 'فئة المنتج', 'Product category'),
(11, 'inventory', 'product', 'unit_id', 'وحدة القياس', 'Unit', 'INT DEFAULT NULL', 'select', NULL, '{"source": "inventory_units", "display": "unit_name_ar", "value": "unit_id"}', NULL, 0, 0, 'classification', 11, 'وحدة قياس المنتج', 'Product unit of measure'),
(12, 'inventory', 'product', 'product_type', 'نوع المنتج', 'Product Type', 'ENUM(\'product\',\'service\',\'digital\') DEFAULT \'product\'', 'select', NULL, '["product", "service", "digital"]', 'product', 0, 0, 'classification', 12, 'نوع المنتج', 'Product type'),

-- حقول الأسعار
(13, 'inventory', 'product', 'cost_price', 'سعر التكلفة', 'Cost Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 13, 'سعر تكلفة المنتج', 'Product cost price'),
(14, 'inventory', 'product', 'selling_price', 'سعر البيع', 'Selling Price', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 0, 0, 'pricing', 14, 'سعر بيع المنتج', 'Product selling price'),

-- حقول المخزون
(15, 'inventory', 'product', 'track_inventory', 'تتبع المخزون', 'Track Inventory', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 0, 0, 'inventory', 15, 'هل يتم تتبع مخزون المنتج', 'Track product inventory'),
(16, 'inventory', 'product', 'min_stock_level', 'الحد الأدنى للمخزون', 'Min Stock Level', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 16, 'الحد الأدنى للمخزون', 'Minimum stock level'),
(17, 'inventory', 'product', 'max_stock_level', 'الحد الأقصى للمخزون', 'Max Stock Level', 'DECIMAL(15,2) DEFAULT NULL', 'number', '{"min": 0}', NULL, NULL, 0, 0, 'inventory', 17, 'الحد الأقصى للمخزون', 'Maximum stock level'),
(18, 'inventory', 'product', 'reorder_point', 'نقطة إعادة الطلب', 'Reorder Point', 'DECIMAL(15,2) DEFAULT 0.00', 'number', '{"min": 0}', NULL, '0.00', 0, 0, 'inventory', 18, 'نقطة إعادة طلب المنتج', 'Product reorder point'),

-- الخصائص الفيزيائية
(19, 'inventory', 'product', 'weight', 'الوزن', 'Weight', 'DECIMAL(10,3) DEFAULT NULL', 'number', '{"min": 0, "step": 0.001}', NULL, NULL, 0, 0, 'physical', 19, 'وزن المنتج', 'Product weight'),
(20, 'inventory', 'product', 'dimensions', 'الأبعاد', 'Dimensions', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 20, 'أبعاد المنتج', 'Product dimensions'),

-- حقول إضافية متقدمة
(21, 'inventory', 'product', 'color', 'اللون', 'Color', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["أحمر", "أزرق", "أخضر", "أصفر", "أسود", "أبيض", "بني", "رمادي", "بنفسجي", "برتقالي"]', NULL, 0, 0, 'physical', 21, 'لون المنتج', 'Product color'),
(22, 'inventory', 'product', 'size', 'الحجم', 'Size', 'VARCHAR(20) DEFAULT NULL', 'select', NULL, '["XS", "S", "M", "L", "XL", "XXL", "صغير", "متوسط", "كبير", "كبير<|im_start|>"]', NULL, 0, 0, 'physical', 22, 'حجم المنتج', 'Product size'),
(23, 'inventory', 'product', 'material', 'المادة', 'Material', 'VARCHAR(100) DEFAULT NULL', 'text', '{"max_length": 100}', NULL, NULL, 0, 0, 'physical', 23, 'مادة صنع المنتج', 'Product material'),
(24, 'inventory', 'product', 'warranty_period', 'فترة الضمان', 'Warranty Period', 'INT DEFAULT NULL', 'number', '{"min": 0, "max": 120}', NULL, NULL, 0, 0, 'warranty', 24, 'فترة الضمان بالشهور', 'Warranty period in months'),
(25, 'inventory', 'product', 'expiry_date', 'تاريخ الانتهاء', 'Expiry Date', 'DATE DEFAULT NULL', 'date', NULL, NULL, NULL, 0, 0, 'dates', 25, 'تاريخ انتهاء صلاحية المنتج', 'Product expiry date'),
(26, 'inventory', 'product', 'supplier_code', 'كود المورد', 'Supplier Code', 'VARCHAR(50) DEFAULT NULL', 'text', '{"max_length": 50}', NULL, NULL, 0, 0, 'supplier', 26, 'كود المنتج عند المورد', 'Product code at supplier'),
(27, 'inventory', 'product', 'origin_country', 'بلد المنشأ', 'Origin Country', 'VARCHAR(50) DEFAULT NULL', 'select', NULL, '["السعودية", "الإمارات", "مصر", "الأردن", "الكويت", "قطر", "البحرين", "عمان", "الصين", "ألمانيا", "أمريكا", "اليابان"]', NULL, 0, 0, 'supplier', 27, 'بلد منشأ المنتج', 'Product origin country'),
(28, 'inventory', 'product', 'is_fragile', 'قابل للكسر', 'Fragile', 'TINYINT(1) DEFAULT 0', 'checkbox', NULL, NULL, '0', 0, 0, 'shipping', 28, 'هل المنتج قابل للكسر', 'Is product fragile'),
(29, 'inventory', 'product', 'image_url', 'صورة المنتج', 'Product Image', 'VARCHAR(255) DEFAULT NULL', 'file', '{"accept": "image/*"}', NULL, NULL, 0, 0, 'media', 29, 'رابط صورة المنتج', 'Product image URL'),
(30, 'inventory', 'product', 'tax_rate', 'معدل الضريبة', 'Tax Rate', 'DECIMAL(5,2) DEFAULT 0.00', 'number', '{"min": 0, "max": 100, "step": 0.01}', NULL, '0.00', 0, 0, 'tax', 30, 'معدل ضريبة المنتج', 'Product tax rate'),

-- حقول النظام (إجبارية)
(31, 'inventory', 'product', 'is_active', 'نشط', 'Active', 'TINYINT(1) DEFAULT 1', 'checkbox', NULL, NULL, '1', 1, 0, 'core', 31, 'حالة نشاط المنتج', 'Product active status'),
(32, 'inventory', 'product', 'created_by', 'أنشئ بواسطة', 'Created By', 'INT NOT NULL', 'hidden', NULL, NULL, NULL, 1, 1, 'core', 32, 'المستخدم الذي أنشأ المنتج', 'User who created the product'),
(33, 'inventory', 'product', 'updated_by', 'حُدث بواسطة', 'Updated By', 'INT DEFAULT NULL', 'hidden', NULL, NULL, NULL, 1, 0, 'core', 33, 'المستخدم الذي حدث المنتج', 'User who updated the product'),
(34, 'inventory', 'product', 'created_at', 'تاريخ الإنشاء', 'Created At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP', 1, 0, 'core', 34, 'تاريخ إنشاء المنتج', 'Product creation date'),
(35, 'inventory', 'product', 'updated_at', 'تاريخ التحديث', 'Updated At', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 1, 0, 'core', 35, 'تاريخ آخر تحديث للمنتج', 'Product last update date');

-- ========================================
-- بيانات افتراضية للشركة رقم 4
-- ========================================

-- إعدادات حقول الشركة رقم 4 (مثال شامل)
INSERT INTO `company_entity_structure` (`company_id`, `module_code`, `entity_type`, `field_id`, `is_enabled`, `is_required`, `is_visible`, `is_searchable`, `is_listable`, `display_order`, `custom_label_ar`, `custom_label_en`, `field_group`, `configured_by`) VALUES

-- الحقول الأساسية (إجبارية)
(4, 'inventory', 'product', 1, 1, 0, 0, 0, 0, 1, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 2, 1, 1, 0, 0, 0, 2, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 3, 1, 0, 0, 0, 0, 3, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 4, 1, 1, 1, 1, 1, 4, 'كود المنتج', 'Product Code', 'basic', 35),
(4, 'inventory', 'product', 5, 1, 1, 1, 1, 1, 5, 'اسم المنتج', 'Product Name', 'basic', 35),

-- الحقول الاختيارية المفعلة
(4, 'inventory', 'product', 6, 1, 0, 1, 1, 1, 6, 'الاسم بالإنجليزية', 'English Name', 'basic', 35),
(4, 'inventory', 'product', 7, 1, 0, 1, 1, 1, 7, 'الباركود', 'Barcode', 'basic', 35),
(4, 'inventory', 'product', 8, 1, 0, 1, 1, 0, 8, 'الوصف', 'Description', 'basic', 35),
(4, 'inventory', 'product', 10, 1, 1, 1, 1, 1, 10, 'الفئة', 'Category', 'classification', 35),
(4, 'inventory', 'product', 11, 1, 1, 1, 1, 1, 11, 'وحدة القياس', 'Unit', 'classification', 35),
(4, 'inventory', 'product', 12, 1, 0, 1, 1, 1, 12, 'نوع المنتج', 'Product Type', 'classification', 35),

-- حقول الأسعار
(4, 'inventory', 'product', 13, 1, 1, 1, 0, 1, 13, 'سعر التكلفة', 'Cost Price', 'pricing', 35),
(4, 'inventory', 'product', 14, 1, 1, 1, 0, 1, 14, 'سعر البيع', 'Selling Price', 'pricing', 35),

-- حقول المخزون
(4, 'inventory', 'product', 15, 1, 0, 1, 0, 0, 15, 'تتبع المخزون', 'Track Inventory', 'inventory', 35),
(4, 'inventory', 'product', 16, 1, 0, 1, 0, 0, 16, 'الحد الأدنى للمخزون', 'Min Stock Level', 'inventory', 35),
(4, 'inventory', 'product', 18, 1, 0, 1, 0, 0, 18, 'نقطة إعادة الطلب', 'Reorder Point', 'inventory', 35),

-- الخصائص الفيزيائية
(4, 'inventory', 'product', 19, 1, 0, 1, 0, 1, 19, 'الوزن (كجم)', 'Weight (KG)', 'physical', 35),
(4, 'inventory', 'product', 21, 1, 0, 1, 1, 1, 21, 'اللون', 'Color', 'physical', 35),
(4, 'inventory', 'product', 22, 1, 0, 1, 1, 1, 22, 'الحجم', 'Size', 'physical', 35),

-- حقول متقدمة
(4, 'inventory', 'product', 24, 1, 0, 1, 0, 0, 24, 'فترة الضمان (شهر)', 'Warranty (Months)', 'warranty', 35),
(4, 'inventory', 'product', 27, 1, 0, 1, 1, 1, 27, 'بلد المنشأ', 'Origin Country', 'supplier', 35),
(4, 'inventory', 'product', 29, 1, 0, 1, 0, 0, 29, 'صورة المنتج', 'Product Image', 'media', 35),

-- حقول النظام (إجبارية)
(4, 'inventory', 'product', 31, 1, 0, 1, 0, 1, 31, 'نشط', 'Active', 'core', 35),
(4, 'inventory', 'product', 32, 1, 1, 0, 0, 0, 32, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 33, 1, 0, 0, 0, 0, 33, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 34, 1, 0, 0, 0, 0, 34, NULL, NULL, 'core', 35),
(4, 'inventory', 'product', 35, 1, 0, 0, 0, 0, 35, NULL, NULL, 'core', 35);

-- تسجيل الجدول الديناميكي للشركة رقم 4
INSERT INTO `dynamic_tables_registry` (`company_id`, `module_code`, `entity_type`, `table_name`, `table_structure`, `is_created`, `created_by`) VALUES
(4, 'inventory', 'product', 'company_4_inventory_product',
'{"fields": [
  {"name": "product_id", "type": "INT NOT NULL AUTO_INCREMENT PRIMARY KEY"},
  {"name": "company_id", "type": "INT NOT NULL"},
  {"name": "module_code", "type": "VARCHAR(50) NOT NULL DEFAULT \'inventory\'"},
  {"name": "product_code", "type": "VARCHAR(50) NOT NULL"},
  {"name": "product_name_ar", "type": "VARCHAR(200) NOT NULL"},
  {"name": "product_name_en", "type": "VARCHAR(200) DEFAULT NULL"},
  {"name": "barcode", "type": "VARCHAR(100) DEFAULT NULL"},
  {"name": "description_ar", "type": "TEXT DEFAULT NULL"},
  {"name": "category_id", "type": "INT DEFAULT NULL"},
  {"name": "unit_id", "type": "INT DEFAULT NULL"},
  {"name": "product_type", "type": "ENUM(\'product\',\'service\',\'digital\') DEFAULT \'product\'"},
  {"name": "cost_price", "type": "DECIMAL(15,2) DEFAULT 0.00"},
  {"name": "selling_price", "type": "DECIMAL(15,2) DEFAULT 0.00"},
  {"name": "track_inventory", "type": "TINYINT(1) DEFAULT 1"},
  {"name": "min_stock_level", "type": "DECIMAL(15,2) DEFAULT 0.00"},
  {"name": "reorder_point", "type": "DECIMAL(15,2) DEFAULT 0.00"},
  {"name": "weight", "type": "DECIMAL(10,3) DEFAULT NULL"},
  {"name": "color", "type": "VARCHAR(50) DEFAULT NULL"},
  {"name": "size", "type": "VARCHAR(20) DEFAULT NULL"},
  {"name": "warranty_period", "type": "INT DEFAULT NULL"},
  {"name": "origin_country", "type": "VARCHAR(50) DEFAULT NULL"},
  {"name": "image_url", "type": "VARCHAR(255) DEFAULT NULL"},
  {"name": "is_active", "type": "TINYINT(1) DEFAULT 1"},
  {"name": "created_by", "type": "INT NOT NULL"},
  {"name": "updated_by", "type": "INT DEFAULT NULL"},
  {"name": "created_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"},
  {"name": "updated_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"}
], "indexes": [
  {"name": "idx_company_code", "columns": ["company_id", "product_code"], "unique": true},
  {"name": "idx_company_name", "columns": ["company_id", "product_name_ar"]},
  {"name": "idx_barcode", "columns": ["barcode"]},
  {"name": "idx_category", "columns": ["category_id"]},
  {"name": "idx_unit", "columns": ["unit_id"]},
  {"name": "idx_active", "columns": ["is_active"]}
]}', 1, 35);

-- ========================================
-- إنشاء الجدول الديناميكي الفعلي للشركة رقم 4
-- ========================================

CREATE TABLE `company_4_inventory_product` (
  `product_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL DEFAULT 4,
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory',
  `product_code` varchar(50) NOT NULL,
  `product_name_ar` varchar(200) NOT NULL,
  `product_name_en` varchar(200) DEFAULT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `category_id` int DEFAULT NULL,
  `unit_id` int DEFAULT NULL,
  `product_type` enum('product','service','digital') DEFAULT 'product',
  `cost_price` decimal(15,2) DEFAULT 0.00,
  `selling_price` decimal(15,2) DEFAULT 0.00,
  `track_inventory` tinyint(1) DEFAULT 1,
  `min_stock_level` decimal(15,2) DEFAULT 0.00,
  `reorder_point` decimal(15,2) DEFAULT 0.00,
  `weight` decimal(10,3) DEFAULT NULL,
  `color` varchar(50) DEFAULT NULL,
  `size` varchar(20) DEFAULT NULL,
  `warranty_period` int DEFAULT NULL,
  `origin_country` varchar(50) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `idx_company_code` (`company_id`, `product_code`),
  KEY `idx_company_name` (`company_id`, `product_name_ar`),
  KEY `idx_barcode` (`barcode`),
  KEY `idx_category` (`category_id`),
  KEY `idx_unit` (`unit_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ========================================
-- بيانات تجريبية للمنتجات - الشركة رقم 4
-- ========================================

-- إدراج فئات المنتجات (إذا لم تكن موجودة)
INSERT IGNORE INTO `inventory_categories` (`category_id`, `company_id`, `category_name_ar`, `category_name_en`, `description_ar`, `is_active`, `created_by`, `created_at`) VALUES
(1, 4, 'إلكترونيات', 'Electronics', 'الأجهزة الإلكترونية والكهربائية', 1, 35, NOW()),
(2, 4, 'ملابس', 'Clothing', 'الملابس والأزياء', 1, 35, NOW()),
(3, 4, 'أثاث', 'Furniture', 'الأثاث والديكور', 1, 35, NOW()),
(4, 4, 'كتب', 'Books', 'الكتب والمطبوعات', 1, 35, NOW()),
(5, 4, 'رياضة', 'Sports', 'المعدات الرياضية', 1, 35, NOW());

-- إدراج وحدات القياس (إذا لم تكن موجودة)
INSERT IGNORE INTO `inventory_units` (`unit_id`, `company_id`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_active`, `created_by`, `created_at`) VALUES
(1, 4, 'قطعة', 'Piece', 'قطعة', 'pcs', 1, 35, NOW()),
(2, 4, 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, 35, NOW()),
(3, 4, 'متر', 'Meter', 'م', 'm', 1, 35, NOW()),
(4, 4, 'لتر', 'Liter', 'لتر', 'L', 1, 35, NOW()),
(5, 4, 'علبة', 'Box', 'علبة', 'box', 1, 35, NOW());

-- إدراج منتجات تجريبية متنوعة
INSERT INTO `company_4_inventory_product` (
  `product_code`, `product_name_ar`, `product_name_en`, `barcode`, `description_ar`,
  `category_id`, `unit_id`, `product_type`, `cost_price`, `selling_price`,
  `track_inventory`, `min_stock_level`, `reorder_point`, `weight`, `color`, `size`,
  `warranty_period`, `origin_country`, `is_active`, `created_by`
) VALUES

-- منتجات إلكترونية
('ELEC001', 'هاتف ذكي سامسونج جالاكسي', 'Samsung Galaxy Smartphone', '8801234567890', 'هاتف ذكي بشاشة 6.5 بوصة وذاكرة 128 جيجا', 1, 1, 'product', 1200.00, 1500.00, 1, 5.00, 10.00, 0.180, 'أسود', 'M', 24, 'كوريا الجنوبية', 1, 35),

('ELEC002', 'لابتوب ديل انسبايرون', 'Dell Inspiron Laptop', '8801234567891', 'لابتوب بمعالج Intel Core i5 وذاكرة 8 جيجا', 1, 1, 'product', 2500.00, 3200.00, 1, 2.00, 5.00, 2.100, 'فضي', 'L', 12, 'الصين', 1, 35),

('ELEC003', 'سماعات بلوتوث', 'Bluetooth Headphones', '8801234567892', 'سماعات لاسلكية عالية الجودة', 1, 1, 'product', 150.00, 250.00, 1, 10.00, 20.00, 0.300, 'أبيض', 'S', 6, 'الصين', 1, 35),

-- منتجات ملابس
('CLTH001', 'قميص قطني رجالي', 'Men Cotton Shirt', '8801234567893', 'قميص قطني عالي الجودة للرجال', 2, 1, 'product', 45.00, 85.00, 1, 20.00, 50.00, 0.250, 'أزرق', 'L', 0, 'مصر', 1, 35),

('CLTH002', 'فستان نسائي', 'Women Dress', '8801234567894', 'فستان أنيق للمناسبات', 2, 1, 'product', 120.00, 200.00, 1, 15.00, 30.00, 0.400, 'أحمر', 'M', 0, 'تركيا', 1, 35),

('CLTH003', 'حذاء رياضي', 'Sports Shoes', '8801234567895', 'حذاء رياضي مريح للجري', 2, 1, 'product', 180.00, 280.00, 1, 12.00, 25.00, 0.800, 'أسود', '42', 6, 'فيتنام', 1, 35),

-- منتجات أثاث
('FURN001', 'كرسي مكتب', 'Office Chair', '8801234567896', 'كرسي مكتب مريح قابل للتعديل', 3, 1, 'product', 350.00, 550.00, 1, 5.00, 10.00, 15.500, 'أسود', 'L', 24, 'الصين', 1, 35),

('FURN002', 'طاولة خشبية', 'Wooden Table', '8801234567897', 'طاولة خشبية صلبة للطعام', 3, 1, 'product', 800.00, 1200.00, 1, 2.00, 5.00, 25.000, 'بني', 'XL', 60, 'ماليزيا', 1, 35),

-- منتجات كتب
('BOOK001', 'كتاب البرمجة', 'Programming Book', '9781234567890', 'كتاب تعليم البرمجة للمبتدئين', 4, 1, 'product', 25.00, 45.00, 1, 50.00, 100.00, 0.500, NULL, 'S', 0, 'مصر', 1, 35),

('BOOK002', 'رواية أدبية', 'Literary Novel', '9781234567891', 'رواية أدبية مشهورة', 4, 1, 'product', 15.00, 30.00, 1, 30.00, 60.00, 0.300, NULL, 'S', 0, 'لبنان', 1, 35),

-- منتجات رياضية
('SPRT001', 'كرة قدم', 'Football', '8801234567898', 'كرة قدم احترافية', 5, 1, 'product', 80.00, 120.00, 1, 20.00, 40.00, 0.450, 'أبيض', 'M', 12, 'باكستان', 1, 35),

('SPRT002', 'دراجة هوائية', 'Bicycle', '8801234567899', 'دراجة هوائية للكبار', 5, 1, 'product', 450.00, 750.00, 1, 3.00, 8.00, 12.000, 'أحمر', 'L', 24, 'الصين', 1, 35),

-- منتجات خدمية
('SERV001', 'خدمة صيانة', 'Maintenance Service', NULL, 'خدمة صيانة للأجهزة الإلكترونية', 1, 1, 'service', 0.00, 100.00, 0, 0.00, 0.00, NULL, NULL, NULL, 0, 'السعودية', 1, 35),

-- منتجات رقمية
('DIGI001', 'برنامج مكافح الفيروسات', 'Antivirus Software', NULL, 'برنامج حماية من الفيروسات لمدة سنة', 1, 1, 'digital', 50.00, 120.00, 0, 0.00, 0.00, NULL, NULL, NULL, 12, 'أمريكا', 1, 35);

-- ========================================
-- ملخص البيانات المدرجة للشركة رقم 4
-- ========================================

/*
تم إنشاء النظام الديناميكي بالكامل للشركة رقم 4:

✅ الجداول الأساسية:
   - field_library: 35 حقل متاح للمنتجات
   - company_entity_structure: إعدادات 22 حقل مفعل للشركة 4
   - dynamic_tables_registry: تسجيل جدول company_4_inventory_product

✅ الجدول الديناميكي:
   - company_4_inventory_product: جدول مخصص للشركة 4
   - 22 حقل مفعل من أصل 35 حقل متاح
   - فهارس محسنة للأداء

✅ البيانات التجريبية:
   - 5 فئات منتجات (إلكترونيات، ملابس، أثاث، كتب، رياضة)
   - 5 وحدات قياس (قطعة، كجم، متر، لتر، علبة)
   - 14 منتج تجريبي متنوع

✅ الحقول المفعلة للشركة 4:
   الأساسية: كود المنتج، اسم المنتج، الاسم بالإنجليزية، الباركود، الوصف
   التصنيف: الفئة، وحدة القياس، نوع المنتج
   الأسعار: سعر التكلفة، سعر البيع
   المخزون: تتبع المخزون، الحد الأدنى، نقطة إعادة الطلب
   الفيزيائية: الوزن، اللون، الحجم
   متقدمة: فترة الضمان، بلد المنشأ، صورة المنتج
   النظام: نشط، تواريخ الإنشاء والتحديث

✅ المستخدم المكون: رقم 35
✅ تاريخ الإعداد: الحالي (NOW())

للاستخدام:
1. تشغيل هذا الملف في قاعدة البيانات
2. الدخول إلى /inventory/dynamic/products
3. سيظهر النظام الديناميكي جاهز للاستخدام
4. يمكن تعديل الحقول من /inventory/settings/fields
*/

-- ========================================
-- انتهاء ملف إعداد النظام الديناميكي
-- ========================================
