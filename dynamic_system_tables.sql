-- ========================================
-- إنشاء جداول النظام الديناميكي الكامل
-- نظام ديناميكي للحقول والتبويبات مع تحكم كامل
-- ========================================

-- ========================================
-- 1. جدول تبويبات النظام
-- ========================================
CREATE TABLE `system_tabs` (
  `tab_id` int NOT NULL AUTO_INCREMENT,
  `module_name` varchar(50) NOT NULL COMMENT 'اسم الوحدة (inventory, sales, hr)',
  `table_name` varchar(50) NOT NULL COMMENT 'اسم الجدول (products, customers, employees)',
  `tab_name` varchar(50) NOT NULL COMMENT 'اسم التبويب الداخلي',
  `tab_label_ar` varchar(100) NOT NULL COMMENT 'تسمية التبويب بالعربية',
  `tab_label_en` varchar(100) NOT NULL COMMENT 'تسمية التبويب بالإنجليزية',
  `tab_description_ar` text COMMENT 'وصف التبويب بالعربية',
  `tab_description_en` text COMMENT 'وصف التبويب بالإنجليزية',
  `tab_icon` varchar(50) DEFAULT 'fas fa-folder' COMMENT 'أيقونة التبويب',
  `tab_color` varchar(20) DEFAULT '#007bff' COMMENT 'لون التبويب',
  `display_order` int DEFAULT 0 COMMENT 'ترتيب العرض',
  `is_default_enabled` tinyint(1) DEFAULT 1 COMMENT 'مفعل افتراضياً للشركات الجديدة',
  `is_core_tab` tinyint(1) DEFAULT 0 COMMENT 'تبويب أساسي لا يمكن حذفه',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`tab_id`),
  UNIQUE KEY `module_table_tab` (`module_name`, `table_name`, `tab_name`),
  KEY `idx_module_table` (`module_name`, `table_name`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول تبويبات النظام';

-- ========================================
-- 2. جدول حقول النظام
-- ========================================
CREATE TABLE `system_fields` (
  `field_id` int NOT NULL AUTO_INCREMENT,
  `module_name` varchar(50) NOT NULL COMMENT 'اسم الوحدة (inventory, sales, hr)',
  `table_name` varchar(50) NOT NULL COMMENT 'اسم الجدول (products, customers, employees)',
  `tab_id` int DEFAULT NULL COMMENT 'معرف التبويب التابع له الحقل',
  `field_name` varchar(50) NOT NULL COMMENT 'اسم الحقل في قاعدة البيانات',
  `field_label_ar` varchar(100) NOT NULL COMMENT 'تسمية الحقل بالعربية',
  `field_label_en` varchar(100) NOT NULL COMMENT 'تسمية الحقل بالإنجليزية',
  `field_description_ar` text COMMENT 'وصف الحقل بالعربية',
  `field_description_en` text COMMENT 'وصف الحقل بالإنجليزية',
  `field_type` varchar(50) NOT NULL COMMENT 'نوع البيانات (VARCHAR, INT, DECIMAL, TEXT, DATE)',
  `field_length` varchar(20) DEFAULT NULL COMMENT 'طول الحقل (255, 10,2)',
  `input_type` varchar(20) NOT NULL COMMENT 'نوع الإدخال (text, number, select, textarea, date, checkbox, file)',
  `validation_rules` json DEFAULT NULL COMMENT 'قواعد التحقق (required, min, max, pattern)',
  `field_options` json DEFAULT NULL COMMENT 'خيارات الحقل للـ select وغيرها',
  `default_value` varchar(255) DEFAULT NULL COMMENT 'القيمة الافتراضية',
  `placeholder_ar` varchar(100) DEFAULT NULL COMMENT 'النص التوضيحي بالعربية',
  `placeholder_en` varchar(100) DEFAULT NULL COMMENT 'النص التوضيحي بالإنجليزية',
  `help_text_ar` text COMMENT 'نص المساعدة بالعربية',
  `help_text_en` text COMMENT 'نص المساعدة بالإنجليزية',
  `is_required_default` tinyint(1) DEFAULT 0 COMMENT 'مطلوب افتراضياً',
  `is_core_field` tinyint(1) DEFAULT 0 COMMENT 'حقل أساسي لا يمكن حذفه',
  `is_system_field` tinyint(1) DEFAULT 0 COMMENT 'حقل نظام (created_at, updated_at)',
  `display_order` int DEFAULT 0 COMMENT 'ترتيب العرض الافتراضي',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`field_id`),
  FOREIGN KEY (`tab_id`) REFERENCES `system_tabs`(`tab_id`) ON DELETE SET NULL,
  UNIQUE KEY `module_table_field` (`module_name`, `table_name`, `field_name`),
  KEY `idx_module_table` (`module_name`, `table_name`),
  KEY `idx_tab_id` (`tab_id`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول حقول النظام';

-- ========================================
-- 3. جدول اختيارات حقول الشركات (الجدول الرئيسي)
-- ملاحظة: التبويبات تظهر تلقائياً بناءً على الحقول المختارة
-- ========================================
CREATE TABLE `company_field_selections` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT 'معرف الشركة',
  `field_id` int NOT NULL COMMENT 'معرف الحقل',
  
  -- خيارات التفعيل والعرض
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT 'الحقل مفعل للشركة',
  `is_visible_form` tinyint(1) DEFAULT 1 COMMENT 'يظهر في النماذج (إضافة/تعديل)',
  `is_visible_table` tinyint(1) DEFAULT 1 COMMENT 'يظهر في جداول العرض',
  `is_visible_details` tinyint(1) DEFAULT 1 COMMENT 'يظهر في صفحة التفاصيل',
  `is_hidden` tinyint(1) DEFAULT 0 COMMENT 'حقل مخفي (hidden input)',
  
  -- خيارات السلوك
  `is_required` tinyint(1) DEFAULT 0 COMMENT 'مطلوب في النماذج',
  `is_searchable` tinyint(1) DEFAULT 1 COMMENT 'قابل للبحث',
  `is_filterable` tinyint(1) DEFAULT 0 COMMENT 'قابل للفلترة',
  `is_sortable` tinyint(1) DEFAULT 1 COMMENT 'قابل للترتيب في الجداول',
  `is_exportable` tinyint(1) DEFAULT 1 COMMENT 'يظهر في التصدير',
  
  -- خيارات التخصيص
  `custom_label_ar` varchar(100) DEFAULT NULL COMMENT 'تسمية مخصصة بالعربية',
  `custom_label_en` varchar(100) DEFAULT NULL COMMENT 'تسمية مخصصة بالإنجليزية',
  `custom_placeholder_ar` varchar(100) DEFAULT NULL COMMENT 'نص توضيحي مخصص بالعربية',
  `custom_placeholder_en` varchar(100) DEFAULT NULL COMMENT 'نص توضيحي مخصص بالإنجليزية',
  `custom_help_text_ar` text DEFAULT NULL COMMENT 'نص مساعدة مخصص بالعربية',
  `custom_help_text_en` text DEFAULT NULL COMMENT 'نص مساعدة مخصص بالإنجليزية',
  `custom_validation_rules` json DEFAULT NULL COMMENT 'قواعد تحقق مخصصة',
  `custom_options` json DEFAULT NULL COMMENT 'خيارات مخصصة للحقل',
  
  -- خيارات الترتيب والتخطيط
  `display_order_form` int DEFAULT 0 COMMENT 'ترتيب في النماذج',
  `display_order_table` int DEFAULT 0 COMMENT 'ترتيب في الجداول',
  `column_width` varchar(20) DEFAULT NULL COMMENT 'عرض العمود في الجداول (px أو %)',
  `field_group` varchar(50) DEFAULT 'general' COMMENT 'مجموعة الحقل للتجميع',
  
  -- معلومات النظام
  `configured_by` int NOT NULL COMMENT 'المستخدم الذي قام بالإعداد',
  `configured_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإعداد',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`) ON DELETE CASCADE,
  FOREIGN KEY (`field_id`) REFERENCES `system_fields`(`field_id`) ON DELETE CASCADE,
  FOREIGN KEY (`configured_by`) REFERENCES `users`(`UserID`) ON DELETE RESTRICT,
  UNIQUE KEY `company_field` (`company_id`, `field_id`),
  KEY `idx_company_enabled` (`company_id`, `is_enabled`),
  KEY `idx_company_form_visible` (`company_id`, `is_visible_form`, `display_order_form`),
  KEY `idx_company_table_visible` (`company_id`, `is_visible_table`, `display_order_table`),
  KEY `idx_company_searchable` (`company_id`, `is_searchable`),
  KEY `idx_company_filterable` (`company_id`, `is_filterable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول اختيارات حقول الشركات';

-- ========================================
-- 4. جدول قيم الحقول الديناميكية
-- ========================================
CREATE TABLE `dynamic_field_values` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT 'معرف الشركة',
  `module_name` varchar(50) NOT NULL COMMENT 'اسم الوحدة',
  `table_name` varchar(50) NOT NULL COMMENT 'اسم الجدول',
  `record_id` int NOT NULL COMMENT 'معرف السجل (مثل product_id)',
  `field_id` int NOT NULL COMMENT 'معرف الحقل',
  `field_value` text COMMENT 'قيمة الحقل',
  `created_by` int NOT NULL COMMENT 'المستخدم الذي أنشأ القيمة',
  `updated_by` int DEFAULT NULL COMMENT 'المستخدم الذي حدث القيمة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`) ON DELETE CASCADE,
  FOREIGN KEY (`field_id`) REFERENCES `system_fields`(`field_id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`UserID`) ON DELETE RESTRICT,
  FOREIGN KEY (`updated_by`) REFERENCES `users`(`UserID`) ON DELETE RESTRICT,
  UNIQUE KEY `company_record_field` (`company_id`, `module_name`, `table_name`, `record_id`, `field_id`),
  KEY `idx_company_module_table` (`company_id`, `module_name`, `table_name`),
  KEY `idx_record_field` (`record_id`, `field_id`),
  KEY `idx_field_value` (`field_id`, `field_value`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول قيم الحقول الديناميكية';

-- ========================================
-- إدراج البيانات الأساسية للنظام
-- ========================================

-- ========================================
-- تبويبات وحدة المخزون - المنتجات
-- ========================================
INSERT INTO `system_tabs` (`tab_id`, `module_name`, `table_name`, `tab_name`, `tab_label_ar`, `tab_label_en`, `tab_description_ar`, `tab_description_en`, `tab_icon`, `tab_color`, `display_order`, `is_default_enabled`, `is_core_tab`) VALUES

-- التبويبات الأساسية للمنتجات
(1, 'inventory', 'products', 'basic_info', 'المعلومات الأساسية', 'Basic Information', 'المعلومات الأساسية للمنتج مثل الاسم والكود', 'Basic product information like name and code', 'fas fa-info-circle', '#007bff', 1, 1, 1),
(2, 'inventory', 'products', 'classification', 'التصنيف', 'Classification', 'تصنيف المنتج والفئة ووحدة القياس', 'Product classification, category and unit of measure', 'fas fa-tags', '#28a745', 2, 1, 1),
(3, 'inventory', 'products', 'pricing', 'الأسعار', 'Pricing', 'أسعار التكلفة والبيع والهوامش', 'Cost prices, selling prices and margins', 'fas fa-dollar-sign', '#ffc107', 3, 1, 0),
(4, 'inventory', 'products', 'inventory', 'المخزون', 'Inventory', 'إعدادات المخزون والحدود الدنيا والعليا', 'Inventory settings, minimum and maximum levels', 'fas fa-boxes', '#17a2b8', 4, 1, 0),
(5, 'inventory', 'products', 'physical', 'الخصائص الفيزيائية', 'Physical Properties', 'الوزن والأبعاد واللون والحجم', 'Weight, dimensions, color and size', 'fas fa-cube', '#6f42c1', 5, 0, 0),
(6, 'inventory', 'products', 'warranty', 'الضمان والصيانة', 'Warranty & Maintenance', 'فترة الضمان ومعلومات الصيانة', 'Warranty period and maintenance information', 'fas fa-shield-alt', '#fd7e14', 6, 0, 0),
(7, 'inventory', 'products', 'supplier', 'معلومات المورد', 'Supplier Information', 'معلومات المورد وكود المنتج عنده', 'Supplier information and product code at supplier', 'fas fa-truck', '#20c997', 7, 0, 0),
(8, 'inventory', 'products', 'shipping', 'الشحن والتوصيل', 'Shipping & Delivery', 'معلومات الشحن والتعبئة والتغليف', 'Shipping, packaging and delivery information', 'fas fa-shipping-fast', '#e83e8c', 8, 0, 0),
(9, 'inventory', 'products', 'media', 'الوسائط', 'Media', 'الصور والملفات المرفقة', 'Images and attached files', 'fas fa-images', '#6c757d', 9, 0, 0),
(10, 'inventory', 'products', 'tax', 'الضرائب', 'Tax', 'إعدادات الضرائب والرسوم', 'Tax and fees settings', 'fas fa-percentage', '#dc3545', 10, 0, 0);

-- ========================================
-- حقول وحدة المخزون - المنتجات
-- ========================================
INSERT INTO `system_fields` (`field_id`, `module_name`, `table_name`, `tab_id`, `field_name`, `field_label_ar`, `field_label_en`, `field_description_ar`, `field_description_en`, `field_type`, `field_length`, `input_type`, `validation_rules`, `field_options`, `default_value`, `placeholder_ar`, `placeholder_en`, `help_text_ar`, `help_text_en`, `is_required_default`, `is_core_field`, `is_system_field`, `display_order`) VALUES

-- الحقول الأساسية (تبويب المعلومات الأساسية)
(1, 'inventory', 'products', 1, 'product_id', 'معرف المنتج', 'Product ID', 'المعرف الفريد للمنتج', 'Unique product identifier', 'INT', NULL, 'hidden', '{"auto_increment": true}', NULL, NULL, NULL, NULL, 'معرف تلقائي للمنتج', 'Auto-generated product ID', 0, 1, 1, 1),
(2, 'inventory', 'products', 1, 'company_id', 'معرف الشركة', 'Company ID', 'معرف الشركة المالكة للمنتج', 'Company ID that owns the product', 'INT', NULL, 'hidden', '{"required": true}', NULL, NULL, NULL, NULL, 'معرف الشركة المالكة', 'Owner company identifier', 1, 1, 1, 2),
(3, 'inventory', 'products', 1, 'product_code', 'كود المنتج', 'Product Code', 'كود المنتج الفريد داخل الشركة', 'Unique product code within company', 'VARCHAR', '50', 'text', '{"required": true, "unique": true, "max_length": 50}', NULL, NULL, 'أدخل كود المنتج', 'Enter product code', 'كود فريد للمنتج لا يتكرر', 'Unique product code', 1, 1, 0, 3),
(4, 'inventory', 'products', 1, 'product_name_ar', 'اسم المنتج', 'Product Name (Arabic)', 'اسم المنتج باللغة العربية', 'Product name in Arabic', 'VARCHAR', '200', 'text', '{"required": true, "max_length": 200}', NULL, NULL, 'أدخل اسم المنتج', 'Enter product name', 'اسم المنتج كما سيظهر في النظام', 'Product name as it will appear in system', 1, 1, 0, 4),
(5, 'inventory', 'products', 1, 'product_name_en', 'الاسم بالإنجليزية', 'Product Name (English)', 'اسم المنتج باللغة الإنجليزية', 'Product name in English', 'VARCHAR', '200', 'text', '{"max_length": 200}', NULL, NULL, 'أدخل الاسم بالإنجليزية', 'Enter English name', 'اسم المنتج باللغة الإنجليزية (اختياري)', 'Product name in English (optional)', 0, 0, 0, 5),
(6, 'inventory', 'products', 1, 'barcode', 'الباركود', 'Barcode', 'رمز الباركود للمنتج', 'Product barcode', 'VARCHAR', '100', 'text', '{"max_length": 100}', NULL, NULL, 'أدخل الباركود', 'Enter barcode', 'رمز الباركود للمسح الضوئي', 'Barcode for scanning', 0, 0, 0, 6),
(7, 'inventory', 'products', 1, 'description_ar', 'الوصف', 'Description (Arabic)', 'وصف تفصيلي للمنتج بالعربية', 'Detailed product description in Arabic', 'TEXT', NULL, 'textarea', NULL, NULL, NULL, 'أدخل وصف المنتج', 'Enter product description', 'وصف تفصيلي يساعد في فهم المنتج', 'Detailed description to help understand the product', 0, 0, 0, 7),
(8, 'inventory', 'products', 1, 'description_en', 'الوصف بالإنجليزية', 'Description (English)', 'وصف تفصيلي للمنتج بالإنجليزية', 'Detailed product description in English', 'TEXT', NULL, 'textarea', NULL, NULL, NULL, 'أدخل الوصف بالإنجليزية', 'Enter English description', 'وصف المنتج باللغة الإنجليزية', 'Product description in English', 0, 0, 0, 8),

-- حقول التصنيف (تبويب التصنيف)
(9, 'inventory', 'products', 2, 'category_id', 'الفئة', 'Category', 'فئة المنتج', 'Product category', 'INT', NULL, 'select', NULL, '{"source": "inventory_categories", "display": "category_name_ar", "value": "category_id"}', NULL, 'اختر الفئة', 'Select category', 'فئة تصنيف المنتج', 'Product classification category', 0, 0, 0, 9),
(10, 'inventory', 'products', 2, 'unit_id', 'وحدة القياس', 'Unit of Measure', 'وحدة قياس المنتج', 'Product unit of measure', 'INT', NULL, 'select', NULL, '{"source": "inventory_units", "display": "unit_name_ar", "value": "unit_id"}', NULL, 'اختر الوحدة', 'Select unit', 'وحدة قياس المنتج (قطعة، كيلو، متر)', 'Product unit of measure (piece, kg, meter)', 0, 0, 0, 10),
(11, 'inventory', 'products', 2, 'product_type', 'نوع المنتج', 'Product Type', 'نوع المنتج (منتج، خدمة، رقمي)', 'Product type (product, service, digital)', 'ENUM', NULL, 'select', NULL, '["منتج", "خدمة", "منتج رقمي"]', 'منتج', 'اختر النوع', 'Select type', 'نوع المنتج يحدد طريقة التعامل معه', 'Product type determines how to handle it', 0, 0, 0, 11),

-- حقول الأسعار (تبويب الأسعار)
(12, 'inventory', 'products', 3, 'cost_price', 'سعر التكلفة', 'Cost Price', 'سعر تكلفة المنتج', 'Product cost price', 'DECIMAL', '15,2', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 'أدخل سعر التكلفة', 'Enter cost price', 'سعر شراء أو تكلفة إنتاج المنتج', 'Purchase price or production cost', 0, 0, 0, 12),
(13, 'inventory', 'products', 3, 'selling_price', 'سعر البيع', 'Selling Price', 'سعر بيع المنتج', 'Product selling price', 'DECIMAL', '15,2', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 'أدخل سعر البيع', 'Enter selling price', 'سعر بيع المنتج للعملاء', 'Product selling price to customers', 0, 0, 0, 13),
(14, 'inventory', 'products', 3, 'wholesale_price', 'سعر الجملة', 'Wholesale Price', 'سعر بيع الجملة', 'Wholesale selling price', 'DECIMAL', '15,2', 'number', '{"min": 0, "step": 0.01}', NULL, '0.00', 'أدخل سعر الجملة', 'Enter wholesale price', 'سعر البيع بالجملة للموزعين', 'Wholesale price for distributors', 0, 0, 0, 14),

-- حقول المخزون (تبويب المخزون)
(15, 'inventory', 'products', 4, 'track_inventory', 'تتبع المخزون', 'Track Inventory', 'هل يتم تتبع مخزون هذا المنتج', 'Whether to track inventory for this product', 'TINYINT', '1', 'checkbox', NULL, NULL, '1', NULL, NULL, 'تفعيل تتبع المخزون للمنتج', 'Enable inventory tracking for product', 0, 0, 0, 15),
(16, 'inventory', 'products', 4, 'current_stock', 'المخزون الحالي', 'Current Stock', 'الكمية الحالية في المخزون', 'Current quantity in stock', 'DECIMAL', '15,3', 'number', '{"min": 0, "step": 0.001}', NULL, '0.000', 'أدخل الكمية', 'Enter quantity', 'الكمية المتوفرة حالياً في المخزون', 'Currently available quantity in stock', 0, 0, 0, 16),
(17, 'inventory', 'products', 4, 'min_stock_level', 'الحد الأدنى للمخزون', 'Minimum Stock Level', 'الحد الأدنى المسموح للمخزون', 'Minimum allowed stock level', 'DECIMAL', '15,3', 'number', '{"min": 0, "step": 0.001}', NULL, '0.000', 'أدخل الحد الأدنى', 'Enter minimum level', 'عند الوصول لهذا الحد يتم التنبيه', 'Alert when reaching this level', 0, 0, 0, 17),
(18, 'inventory', 'products', 4, 'max_stock_level', 'الحد الأقصى للمخزون', 'Maximum Stock Level', 'الحد الأقصى المسموح للمخزون', 'Maximum allowed stock level', 'DECIMAL', '15,3', 'number', '{"min": 0, "step": 0.001}', NULL, NULL, 'أدخل الحد الأقصى', 'Enter maximum level', 'الحد الأقصى المسموح تخزينه', 'Maximum allowed to store', 0, 0, 0, 18),
(19, 'inventory', 'products', 4, 'reorder_point', 'نقطة إعادة الطلب', 'Reorder Point', 'النقطة التي يجب إعادة طلب المنتج عندها', 'Point at which product should be reordered', 'DECIMAL', '15,3', 'number', '{"min": 0, "step": 0.001}', NULL, '0.000', 'أدخل نقطة الطلب', 'Enter reorder point', 'عند الوصول لهذه النقطة يجب إعادة الطلب', 'Reorder when reaching this point', 0, 0, 0, 19),
(20, 'inventory', 'products', 4, 'reorder_quantity', 'كمية إعادة الطلب', 'Reorder Quantity', 'الكمية المقترحة لإعادة الطلب', 'Suggested quantity for reordering', 'DECIMAL', '15,3', 'number', '{"min": 0, "step": 0.001}', NULL, '0.000', 'أدخل كمية الطلب', 'Enter reorder quantity', 'الكمية المقترحة عند إعادة الطلب', 'Suggested quantity when reordering', 0, 0, 0, 20),

-- الخصائص الفيزيائية (تبويب الخصائص الفيزيائية)
(21, 'inventory', 'products', 5, 'weight', 'الوزن', 'Weight', 'وزن المنتج بالكيلوجرام', 'Product weight in kilograms', 'DECIMAL', '10,3', 'number', '{"min": 0, "step": 0.001}', NULL, NULL, 'أدخل الوزن بالكيلو', 'Enter weight in kg', 'وزن المنتج مهم للشحن والتخزين', 'Product weight important for shipping and storage', 0, 0, 0, 21),
(22, 'inventory', 'products', 5, 'length', 'الطول', 'Length', 'طول المنتج بالسنتيمتر', 'Product length in centimeters', 'DECIMAL', '8,2', 'number', '{"min": 0, "step": 0.01}', NULL, NULL, 'أدخل الطول بالسم', 'Enter length in cm', 'طول المنتج للتعبئة والتغليف', 'Product length for packaging', 0, 0, 0, 22),
(23, 'inventory', 'products', 5, 'width', 'العرض', 'Width', 'عرض المنتج بالسنتيمتر', 'Product width in centimeters', 'DECIMAL', '8,2', 'number', '{"min": 0, "step": 0.01}', NULL, NULL, 'أدخل العرض بالسم', 'Enter width in cm', 'عرض المنتج للتعبئة والتغليف', 'Product width for packaging', 0, 0, 0, 23),
(24, 'inventory', 'products', 5, 'height', 'الارتفاع', 'Height', 'ارتفاع المنتج بالسنتيمتر', 'Product height in centimeters', 'DECIMAL', '8,2', 'number', '{"min": 0, "step": 0.01}', NULL, NULL, 'أدخل الارتفاع بالسم', 'Enter height in cm', 'ارتفاع المنتج للتعبئة والتغليف', 'Product height for packaging', 0, 0, 0, 24),
(25, 'inventory', 'products', 5, 'color', 'اللون', 'Color', 'لون المنتج', 'Product color', 'VARCHAR', '50', 'select', NULL, '["أحمر", "أزرق", "أخضر", "أصفر", "أسود", "أبيض", "بني", "رمادي", "بنفسجي", "برتقالي", "وردي", "ذهبي", "فضي"]', NULL, 'اختر اللون', 'Select color', 'لون المنتج الأساسي', 'Primary product color', 0, 0, 0, 25),
(26, 'inventory', 'products', 5, 'size', 'الحجم', 'Size', 'حجم المنتج', 'Product size', 'VARCHAR', '20', 'select', NULL, '["XS", "S", "M", "L", "XL", "XXL", "صغير", "متوسط", "كبير", "كبير جداً"]', NULL, 'اختر الحجم', 'Select size', 'حجم المنتج (للملابس والأحذية)', 'Product size (for clothes and shoes)', 0, 0, 0, 26),
(27, 'inventory', 'products', 5, 'material', 'المادة', 'Material', 'مادة صنع المنتج', 'Product material', 'VARCHAR', '100', 'text', '{"max_length": 100}', NULL, NULL, 'أدخل المادة', 'Enter material', 'المادة المصنوع منها المنتج', 'Material the product is made of', 0, 0, 0, 27),

-- الضمان والصيانة (تبويب الضمان)
(28, 'inventory', 'products', 6, 'warranty_period', 'فترة الضمان', 'Warranty Period', 'فترة الضمان بالشهور', 'Warranty period in months', 'INT', NULL, 'number', '{"min": 0, "max": 120}', NULL, NULL, 'أدخل فترة الضمان', 'Enter warranty period', 'فترة الضمان المقدمة للعميل بالشهور', 'Warranty period offered to customer in months', 0, 0, 0, 28),
(29, 'inventory', 'products', 6, 'warranty_type', 'نوع الضمان', 'Warranty Type', 'نوع الضمان المقدم', 'Type of warranty offered', 'VARCHAR', '50', 'select', NULL, '["ضمان الشركة", "ضمان المحل", "ضمان شامل", "ضمان محدود", "بدون ضمان"]', NULL, 'اختر نوع الضمان', 'Select warranty type', 'نوع الضمان المقدم للعميل', 'Type of warranty offered to customer', 0, 0, 0, 29),
(30, 'inventory', 'products', 6, 'maintenance_schedule', 'جدولة الصيانة', 'Maintenance Schedule', 'جدولة الصيانة الدورية', 'Periodic maintenance schedule', 'VARCHAR', '100', 'text', '{"max_length": 100}', NULL, NULL, 'أدخل جدولة الصيانة', 'Enter maintenance schedule', 'جدولة الصيانة الدورية المطلوبة', 'Required periodic maintenance schedule', 0, 0, 0, 30),

-- معلومات المورد (تبويب المورد)
(31, 'inventory', 'products', 7, 'supplier_id', 'المورد', 'Supplier', 'المورد الأساسي للمنتج', 'Primary supplier for the product', 'INT', NULL, 'select', NULL, '{"source": "suppliers", "display": "supplier_name", "value": "supplier_id"}', NULL, 'اختر المورد', 'Select supplier', 'المورد الأساسي لهذا المنتج', 'Primary supplier for this product', 0, 0, 0, 31),
(32, 'inventory', 'products', 7, 'supplier_code', 'كود المنتج عند المورد', 'Supplier Product Code', 'كود المنتج في نظام المورد', 'Product code in supplier system', 'VARCHAR', '50', 'text', '{"max_length": 50}', NULL, NULL, 'أدخل كود المورد', 'Enter supplier code', 'كود المنتج كما هو في نظام المورد', 'Product code as it is in supplier system', 0, 0, 0, 32),
(33, 'inventory', 'products', 7, 'origin_country', 'بلد المنشأ', 'Origin Country', 'بلد منشأ أو صنع المنتج', 'Country of origin or manufacture', 'VARCHAR', '50', 'select', NULL, '["السعودية", "الإمارات", "مصر", "الأردن", "الكويت", "قطر", "البحرين", "عمان", "الصين", "ألمانيا", "أمريكا", "اليابان", "كوريا الجنوبية", "إيطاليا", "فرنسا", "تركيا"]', NULL, 'اختر بلد المنشأ', 'Select origin country', 'بلد منشأ أو تصنيع المنتج', 'Country where product is originated or manufactured', 0, 0, 0, 33),
(34, 'inventory', 'products', 7, 'lead_time', 'مدة التوريد', 'Lead Time', 'مدة التوريد من المورد بالأيام', 'Lead time from supplier in days', 'INT', NULL, 'number', '{"min": 0, "max": 365}', NULL, NULL, 'أدخل مدة التوريد', 'Enter lead time', 'عدد الأيام المطلوبة للحصول على المنتج من المورد', 'Number of days required to get product from supplier', 0, 0, 0, 34),

-- الشحن والتوصيل (تبويب الشحن)
(35, 'inventory', 'products', 8, 'is_fragile', 'قابل للكسر', 'Fragile', 'هل المنتج قابل للكسر', 'Is the product fragile', 'TINYINT', '1', 'checkbox', NULL, NULL, '0', NULL, NULL, 'تحديد إذا كان المنتج قابل للكسر', 'Specify if product is fragile', 0, 0, 0, 35),
(36, 'inventory', 'products', 8, 'requires_special_handling', 'يتطلب معاملة خاصة', 'Requires Special Handling', 'هل يتطلب المنتج معاملة خاصة', 'Does product require special handling', 'TINYINT', '1', 'checkbox', NULL, NULL, '0', NULL, NULL, 'تحديد إذا كان المنتج يتطلب معاملة خاصة', 'Specify if product requires special handling', 0, 0, 0, 36),
(37, 'inventory', 'products', 8, 'shipping_class', 'فئة الشحن', 'Shipping Class', 'فئة الشحن للمنتج', 'Shipping class for the product', 'VARCHAR', '50', 'select', NULL, '["عادي", "سريع", "مبرد", "مجمد", "خطر", "ثقيل", "كبير الحجم"]', 'عادي', 'اختر فئة الشحن', 'Select shipping class', 'فئة الشحن تحدد طريقة التعامل مع المنتج', 'Shipping class determines how to handle the product', 0, 0, 0, 37),

-- الوسائط (تبويب الوسائط)
(38, 'inventory', 'products', 9, 'image_url', 'صورة المنتج', 'Product Image', 'رابط صورة المنتج الرئيسية', 'Main product image URL', 'VARCHAR', '255', 'file', '{"accept": "image/*", "max_size": "2MB"}', NULL, NULL, 'اختر صورة المنتج', 'Choose product image', 'صورة المنتج الرئيسية للعرض', 'Main product image for display', 0, 0, 0, 38),
(39, 'inventory', 'products', 9, 'gallery_images', 'معرض الصور', 'Image Gallery', 'صور إضافية للمنتج', 'Additional product images', 'TEXT', NULL, 'file', '{"accept": "image/*", "multiple": true, "max_size": "2MB"}', NULL, NULL, 'اختر صور إضافية', 'Choose additional images', 'صور إضافية لعرض المنتج من زوايا مختلفة', 'Additional images to show product from different angles', 0, 0, 0, 39),
(40, 'inventory', 'products', 9, 'video_url', 'فيديو المنتج', 'Product Video', 'رابط فيديو توضيحي للمنتج', 'Product demonstration video URL', 'VARCHAR', '255', 'text', '{"pattern": "url"}', NULL, NULL, 'أدخل رابط الفيديو', 'Enter video URL', 'رابط فيديو يوضح المنتج أو طريقة استخدامه', 'Video URL showing product or how to use it', 0, 0, 0, 40),

-- الضرائب (تبويب الضرائب)
(41, 'inventory', 'products', 10, 'tax_rate', 'معدل الضريبة', 'Tax Rate', 'معدل الضريبة المطبق على المنتج', 'Tax rate applied to the product', 'DECIMAL', '5,2', 'number', '{"min": 0, "max": 100, "step": 0.01}', NULL, '15.00', 'أدخل معدل الضريبة', 'Enter tax rate', 'معدل الضريبة بالنسبة المئوية', 'Tax rate in percentage', 0, 0, 0, 41),
(42, 'inventory', 'products', 10, 'tax_exempt', 'معفى من الضريبة', 'Tax Exempt', 'هل المنتج معفى من الضريبة', 'Is product exempt from tax', 'TINYINT', '1', 'checkbox', NULL, NULL, '0', NULL, NULL, 'تحديد إذا كان المنتج معفى من الضريبة', 'Specify if product is tax exempt', 0, 0, 0, 42),

-- حقول النظام (مخفية)
(43, 'inventory', 'products', 1, 'is_active', 'نشط', 'Active', 'حالة نشاط المنتج', 'Product active status', 'TINYINT', '1', 'checkbox', NULL, NULL, '1', NULL, NULL, 'تحديد إذا كان المنتج نشط أم لا', 'Specify if product is active or not', 0, 1, 0, 43),
(44, 'inventory', 'products', 1, 'created_by', 'أنشئ بواسطة', 'Created By', 'المستخدم الذي أنشأ المنتج', 'User who created the product', 'INT', NULL, 'hidden', NULL, NULL, NULL, NULL, NULL, 'معرف المستخدم الذي أنشأ المنتج', 'ID of user who created the product', 1, 1, 1, 44),
(45, 'inventory', 'products', 1, 'updated_by', 'حُدث بواسطة', 'Updated By', 'المستخدم الذي حدث المنتج', 'User who updated the product', 'INT', NULL, 'hidden', NULL, NULL, NULL, NULL, NULL, 'معرف المستخدم الذي حدث المنتج', 'ID of user who updated the product', 0, 1, 1, 45),
(46, 'inventory', 'products', 1, 'created_at', 'تاريخ الإنشاء', 'Created At', 'تاريخ إنشاء المنتج', 'Product creation date', 'TIMESTAMP', NULL, 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP', NULL, NULL, 'تاريخ ووقت إنشاء المنتج', 'Date and time of product creation', 0, 1, 1, 46),
(47, 'inventory', 'products', 1, 'updated_at', 'تاريخ التحديث', 'Updated At', 'تاريخ آخر تحديث للمنتج', 'Product last update date', 'TIMESTAMP', NULL, 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', NULL, NULL, 'تاريخ ووقت آخر تحديث للمنتج', 'Date and time of last product update', 0, 1, 1, 47);

-- ========================================
-- ملخص النظام الديناميكي
-- ========================================

/*
تم إنشاء النظام الديناميكي الكامل المُصحح:

✅ الجداول الأساسية (4 جداول فقط):
   1. system_tabs: 10 تبويبات ثابتة للمنتجات
   2. system_fields: 47 حقل شامل للمنتجات (مرتبط بالتبويبات)
   3. company_field_selections: اختيارات الحقول لكل شركة (مع تحكم كامل)
   4. dynamic_field_values: قيم الحقول الفعلية

✅ المفهوم المُصحح:
   - التبويبات ثابتة في النظام (لا تختارها الشركة)
   - الشركة تختار الحقول فقط
   - التبويب يظهر تلقائياً إذا اختارت الشركة أي حقل منه
   - التبويب يختفي إذا لم تختر الشركة أي حقل منه

✅ التبويبات المتاحة:
   1. المعلومات الأساسية (8 حقول)
   2. التصنيف (3 حقول)
   3. الأسعار (3 حقول)
   4. المخزون (6 حقول)
   5. الخصائص الفيزيائية (7 حقول)
   6. الضمان والصيانة (3 حقول)
   7. معلومات المورد (4 حقول)
   8. الشحن والتوصيل (3 حقول)
   9. الوسائط (3 حقول)
   10. الضرائب (2 حقول)

✅ أنواع الحقول المدعومة:
   - text: حقول نصية
   - number: حقول رقمية
   - select: قوائم منسدلة
   - textarea: مناطق نص كبيرة
   - checkbox: مربعات اختيار
   - file: رفع ملفات
   - hidden: حقول مخفية

✅ خيارات التحكم لكل حقل:
   - is_enabled: مفعل/معطل
   - is_visible_form: يظهر في النماذج
   - is_visible_table: يظهر في الجداول
   - is_visible_details: يظهر في التفاصيل
   - is_hidden: مخفي تماماً
   - is_required: مطلوب
   - is_searchable: قابل للبحث
   - is_filterable: قابل للفلترة
   - is_sortable: قابل للترتيب
   - is_exportable: يظهر في التصدير

✅ التخصيص المتاح:
   - تسميات مخصصة (عربي/إنجليزي)
   - نصوص توضيحية مخصصة
   - نصوص مساعدة مخصصة
   - قواعد تحقق مخصصة
   - ترتيب مخصص للنماذج والجداول
   - عرض أعمدة مخصص

✅ المرحلة التالية:
   1. إنشاء FieldManager class (إدارة الحقول)
   2. تطوير ProductController ليستخدم النظام الديناميكي
   3. تطوير Product Model ليعمل مع dynamic_field_values
   4. إنشاء واجهة إدارة الحقول للشركات (بدون تبويبات)
   5. تطوير العروض الديناميكية (التبويبات تظهر تلقائياً)
   6. تطوير منطق إظهار/إخفاء التبويبات بناءً على الحقول المختارة

✅ منطق عمل التبويبات:
   ```php
   // الحصول على الحقول المختارة للشركة
   $selectedFields = getCompanySelectedFields($companyId);

   // تجميع الحقول حسب التبويبات
   $visibleTabs = [];
   foreach($selectedFields as $field) {
       $tabId = $field['tab_id'];
       if (!isset($visibleTabs[$tabId])) {
           $visibleTabs[$tabId] = getSystemTab($tabId);
           $visibleTabs[$tabId]['fields'] = [];
       }
       $visibleTabs[$tabId]['fields'][] = $field;
   }

   // عرض التبويبات التي تحتوي على حقول فقط
   foreach($visibleTabs as $tab) {
       // عرض التبويب مع حقوله
   }
   ```

النظام جاهز للتطبيق! 🎉
*/

-- ========================================
-- انتهاء ملف إنشاء جداول النظام الديناميكي
-- ========================================
