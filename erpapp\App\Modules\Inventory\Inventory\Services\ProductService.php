<?php
namespace App\Modules\Inventory\Services;

use App\Modules\Inventory\Models\Product;
use App\Modules\Inventory\Models\Category;
use App\Modules\Inventory\Models\Unit;
use Exception;

/**
 * Product Service - خدمة المنتجات
 */
class ProductService
{
    protected $productModel;
    protected $categoryModel;
    protected $unitModel;

    public function __construct()
    {
        $this->productModel = new Product();
        $this->categoryModel = new Category();
        $this->unitModel = new Unit();
    }

    /**
     * الحصول على الفئات للشركة
     */
    public function getCategories($company_id)
    {
        return $this->categoryModel->getByCompany($company_id, ['is_active' => 1]);
    }

    /**
     * الحصول على وحدات القياس للشركة
     */
    public function getUnits($company_id)
    {
        return $this->unitModel->getByCompany($company_id, ['is_active' => 1]);
    }

    /**
     * الحصول على أرصدة المنتج
     */
    public function getProductStock($product_id, $company_id)
    {
        global $db;

        $sql = "SELECT
                    w.warehouse_name_ar,
                    s.quantity_on_hand,
                    s.quantity_available,
                    s.quantity_reserved,
                    s.last_updated
                FROM inventory_stock s
                LEFT JOIN inventory_warehouses w ON s.warehouse_id = w.warehouse_id
                WHERE s.product_id = ? AND s.company_id = ?
                ORDER BY w.warehouse_name_ar";

        $stmt = $db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء منتج جديد مع المخزون الأولي
     */
    public function createProductWithStock($productData, $initialStock = [])
    {
        global $db;

        try {
            $db->beginTransaction();

            // إنشاء المنتج
            $product_id = $this->productModel->create($productData);

            if (!$product_id) {
                throw new Exception('فشل في إنشاء المنتج');
            }

            // إضافة المخزون الأولي إذا كان موجوداً
            if (!empty($initialStock) && $productData['track_inventory']) {
                foreach ($initialStock as $stock) {
                    $this->addInitialStock($product_id, $productData['company_id'], $stock);
                }
            }

            $db->commit();
            return $product_id;

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * إضافة مخزون أولي للمنتج
     */
    private function addInitialStock($product_id, $company_id, $stockData)
    {
        global $db;

        $sql = "INSERT INTO inventory_stock (
                    company_id, product_id, warehouse_id,
                    quantity_on_hand, quantity_available, quantity_reserved,
                    cost_per_unit, last_updated, created_by
                ) VALUES (?, ?, ?, ?, ?, 0, ?, NOW(), ?)";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            $company_id,
            $product_id,
            $stockData['warehouse_id'],
            $stockData['quantity'],
            $stockData['quantity'],
            $stockData['cost_per_unit'] ?? 0,
            $stockData['created_by']
        ]);

        // إضافة حركة مخزون للمخزون الأولي
        $this->addStockMovement($product_id, $company_id, [
            'warehouse_id' => $stockData['warehouse_id'],
            'movement_type' => 'initial_stock',
            'quantity' => $stockData['quantity'],
            'cost_per_unit' => $stockData['cost_per_unit'] ?? 0,
            'reference_type' => 'initial',
            'reference_id' => $product_id,
            'notes' => 'مخزون أولي',
            'created_by' => $stockData['created_by']
        ]);
    }

    /**
     * إضافة حركة مخزون
     */
    public function addStockMovement($product_id, $company_id, $movementData)
    {
        global $db;

        $sql = "INSERT INTO inventory_movements (
                    company_id, product_id, warehouse_id, movement_type,
                    quantity, cost_per_unit, total_cost,
                    reference_type, reference_id, notes,
                    movement_date, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW())";

        $total_cost = $movementData['quantity'] * ($movementData['cost_per_unit'] ?? 0);

        $stmt = $db->prepare($sql);
        return $stmt->execute([
            $company_id,
            $product_id,
            $movementData['warehouse_id'],
            $movementData['movement_type'],
            $movementData['quantity'],
            $movementData['cost_per_unit'] ?? 0,
            $total_cost,
            $movementData['reference_type'] ?? 'manual',
            $movementData['reference_id'] ?? null,
            $movementData['notes'] ?? '',
            $movementData['created_by']
        ]);
    }

    /**
     * تحديث أرصدة المخزون
     */
    public function updateStock($product_id, $warehouse_id, $company_id, $quantity_change, $movement_type = 'adjustment')
    {
        global $db;

        try {
            $db->beginTransaction();

            // الحصول على الرصيد الحالي
            $sql = "SELECT * FROM inventory_stock
                    WHERE product_id = ? AND warehouse_id = ? AND company_id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$product_id, $warehouse_id, $company_id]);
            $currentStock = $stmt->fetch(\PDO::FETCH_ASSOC);

            if ($currentStock) {
                // تحديث الرصيد الموجود
                $new_quantity = $currentStock['quantity_on_hand'] + $quantity_change;
                $new_available = $currentStock['quantity_available'] + $quantity_change;

                $sql = "UPDATE inventory_stock SET
                        quantity_on_hand = ?, quantity_available = ?, last_updated = NOW()
                        WHERE product_id = ? AND warehouse_id = ? AND company_id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$new_quantity, $new_available, $product_id, $warehouse_id, $company_id]);
            } else {
                // إنشاء رصيد جديد
                $sql = "INSERT INTO inventory_stock (
                        company_id, product_id, warehouse_id,
                        quantity_on_hand, quantity_available, quantity_reserved,
                        last_updated
                        ) VALUES (?, ?, ?, ?, ?, 0, NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$company_id, $product_id, $warehouse_id, $quantity_change, $quantity_change]);
            }

            // إضافة حركة المخزون
            $this->addStockMovement($product_id, $company_id, [
                'warehouse_id' => $warehouse_id,
                'movement_type' => $movement_type,
                'quantity' => $quantity_change,
                'created_by' => current_user_id()
            ]);

            $db->commit();
            return true;

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على تقرير المخزون للمنتج
     */
    public function getProductStockReport($product_id, $company_id)
    {
        global $db;

        $sql = "SELECT
                    p.product_name_ar,
                    p.product_code,
                    p.min_stock_level,
                    p.max_stock_level,
                    w.warehouse_name_ar,
                    s.quantity_on_hand,
                    s.quantity_available,
                    s.quantity_reserved,
                    s.cost_per_unit,
                    (s.quantity_on_hand * s.cost_per_unit) as total_value
                FROM inventory_products p
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                LEFT JOIN inventory_warehouses w ON s.warehouse_id = w.warehouse_id
                WHERE p.product_id = ? AND p.company_id = ?
                ORDER BY w.warehouse_name_ar";

        $stmt = $db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على حركات المخزون للمنتج
     */
    public function getProductMovements($product_id, $company_id, $limit = 50)
    {
        global $db;

        $sql = "SELECT
                    m.*,
                    w.warehouse_name_ar,
                    u.FirstName, u.LastName
                FROM inventory_movements m
                LEFT JOIN inventory_warehouses w ON m.warehouse_id = w.warehouse_id
                LEFT JOIN users u ON m.created_by = u.UserID
                WHERE m.product_id = ? AND m.company_id = ?
                ORDER BY m.movement_date DESC, m.created_at DESC
                LIMIT ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$product_id, $company_id, $limit]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * التحقق من توفر المخزون
     */
    public function checkStockAvailability($product_id, $warehouse_id, $company_id, $required_quantity)
    {
        global $db;

        $sql = "SELECT quantity_available FROM inventory_stock
                WHERE product_id = ? AND warehouse_id = ? AND company_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$product_id, $warehouse_id, $company_id]);
        $available = $stmt->fetchColumn();

        return ($available >= $required_quantity);
    }

    /**
     * حجز كمية من المخزون
     */
    public function reserveStock($product_id, $warehouse_id, $company_id, $quantity, $reference_type, $reference_id)
    {
        global $db;

        try {
            $db->beginTransaction();

            // التحقق من توفر الكمية
            if (!$this->checkStockAvailability($product_id, $warehouse_id, $company_id, $quantity)) {
                throw new Exception('الكمية المطلوبة غير متوفرة في المخزون');
            }

            // تحديث الأرصدة
            $sql = "UPDATE inventory_stock SET
                    quantity_available = quantity_available - ?,
                    quantity_reserved = quantity_reserved + ?
                    WHERE product_id = ? AND warehouse_id = ? AND company_id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$quantity, $quantity, $product_id, $warehouse_id, $company_id]);

            // إضافة حركة الحجز
            $this->addStockMovement($product_id, $company_id, [
                'warehouse_id' => $warehouse_id,
                'movement_type' => 'reserve',
                'quantity' => -$quantity,
                'reference_type' => $reference_type,
                'reference_id' => $reference_id,
                'notes' => 'حجز مخزون',
                'created_by' => current_user_id()
            ]);

            $db->commit();
            return true;

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * إلغاء حجز المخزون
     */
    public function unreserveStock($product_id, $warehouse_id, $company_id, $quantity, $reference_type, $reference_id)
    {
        global $db;

        try {
            $db->beginTransaction();

            // تحديث الأرصدة
            $sql = "UPDATE inventory_stock SET
                    quantity_available = quantity_available + ?,
                    quantity_reserved = quantity_reserved - ?
                    WHERE product_id = ? AND warehouse_id = ? AND company_id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$quantity, $quantity, $product_id, $warehouse_id, $company_id]);

            // إضافة حركة إلغاء الحجز
            $this->addStockMovement($product_id, $company_id, [
                'warehouse_id' => $warehouse_id,
                'movement_type' => 'unreserve',
                'quantity' => $quantity,
                'reference_type' => $reference_type,
                'reference_id' => $reference_id,
                'notes' => 'إلغاء حجز مخزون',
                'created_by' => current_user_id()
            ]);

            $db->commit();
            return true;

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
}
