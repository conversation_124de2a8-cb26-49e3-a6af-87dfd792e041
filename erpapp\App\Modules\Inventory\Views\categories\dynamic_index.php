<?php
/**
 * عرض المنتجات الديناميكي
 * يعرض المنتجات حسب الحقول المختارة للشركة
 */
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="h4 mb-1">
                <i class="fas fa-boxes text-primary me-2"></i>
                <?= $title ?>
            </h2>
            <p class="text-muted mb-0">إدارة المنتجات بالنظام الديناميكي</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة منتج جديد
            </a>
            <a href="<?= base_url('inventory/settings/fields') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-cog me-1"></i>
                إعدادات الحقول
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- البحث العام -->
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" 
                               placeholder="ابحث في المنتجات..." 
                               value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- فلاتر الحقول القابلة للفلترة -->
                <?php foreach($filterableFields as $field): ?>
                    <div class="col-md-3">
                        <label class="form-label">
                            <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                        </label>
                        
                        <?php if($field['input_type'] == 'select' && !empty($field['field_options'])): ?>
                            <select name="filter_<?= $field['field_name'] ?>" class="form-select">
                                <option value="">الكل</option>
                                <?php 
                                $options = json_decode($field['field_options'], true);
                                if(is_array($options)):
                                    foreach($options as $option):
                                ?>
                                    <option value="<?= htmlspecialchars($option) ?>" 
                                            <?= (($_GET['filter_' . $field['field_name']] ?? '') == $option) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($option) ?>
                                    </option>
                                <?php 
                                    endforeach;
                                endif;
                                ?>
                            </select>
                        <?php else: ?>
                            <input type="text" name="filter_<?= $field['field_name'] ?>" 
                                   class="form-control" 
                                   placeholder="فلترة حسب <?= $field['field_label_ar'] ?>"
                                   value="<?= htmlspecialchars($_GET['filter_' . $field['field_name']] ?? '') ?>">
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>

                <!-- أزرار التحكم -->
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                    <button type="button" class="btn btn-success" onclick="exportProducts()">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المنتجات الديناميكي -->
    <div class="card">
        <div class="card-body">
            <?php if(empty($products)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                    <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                        إضافة منتج جديد
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="50">#</th>
                                <?php foreach($tableFields as $field): ?>
                                    <th style="width: <?= $field['column_width'] ?: 'auto' ?>">
                                        <?php if($field['is_sortable']): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, [
                                                'sort' => $field['field_name'],
                                                'direction' => (($_GET['sort'] ?? '') == $field['field_name'] && ($_GET['direction'] ?? '') == 'asc') ? 'desc' : 'asc'
                                            ])) ?>" class="text-decoration-none text-dark">
                                                <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                                <?php if(($_GET['sort'] ?? '') == $field['field_name']): ?>
                                                    <i class="fas fa-sort-<?= (($_GET['direction'] ?? '') == 'desc') ? 'down' : 'up' ?> ms-1"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-sort ms-1 text-muted"></i>
                                                <?php endif; ?>
                                            </a>
                                        <?php else: ?>
                                            <?= $field['custom_label_ar'] ?: $field['field_label_ar'] ?>
                                        <?php endif; ?>
                                    </th>
                                <?php endforeach; ?>
                                <th width="120">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($products as $index => $product): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <?php foreach($tableFields as $field): ?>
                                        <td>
                                            <?php
                                            $value = $product[$field['field_name']] ?? '';
                                            
                                            // تنسيق خاص للحقول
                                            if($field['field_type'] == 'DECIMAL' && is_numeric($value)) {
                                                echo '<span class="badge bg-info">' . number_format($value, 2) . '</span>';
                                            } elseif($field['input_type'] == 'checkbox') {
                                                echo $value ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>';
                                            } elseif($field['field_name'] == 'product_code') {
                                                echo '<code>' . htmlspecialchars($value) . '</code>';
                                            } elseif(strlen($value) > 50) {
                                                echo '<span title="' . htmlspecialchars($value) . '">' . 
                                                     htmlspecialchars(substr($value, 0, 50)) . '...</span>';
                                            } else {
                                                echo htmlspecialchars($value);
                                            }
                                            ?>
                                        </td>
                                    <?php endforeach; ?>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" 
                                               class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" 
                                               class="btn btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteProduct(<?= $product['product_id'] ?>)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <small class="text-muted">
                            عرض <?= count($products) ?> منتج
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            آخر تحديث: <?= date('Y-m-d H:i') ?>
                        </small>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
function deleteProduct(productId) {
    if(confirm('هل أنت متأكد من حذف هذا المنتج؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('inventory/products/') ?>${productId}/delete`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '<?= csrf_token() ?>';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function exportProducts() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.location.href = '<?= base_url('inventory/products') ?>?' + params.toString();
}

// تحديث تلقائي كل 5 دقائق
setTimeout(() => {
    window.location.reload();
}, 300000);
</script>

<style>
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

code {
    background-color: #f1f3f4;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}
</style>
