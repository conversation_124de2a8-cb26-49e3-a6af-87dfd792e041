<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-boxes"></i> <?= __('إدارة المنتجات') ?> 
                    <span class="badge badge-info">ديناميكي</span>
                </h1>
                <div>
                    <a href="<?= base_url('inventory/settings/fields') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-cogs me-1"></i> <?= __('إعدادات الحقول') ?>
                    </a>
                    <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary me-2">
                        <i class="fas fa-plus me-1"></i> <?= __('إضافة منتج جديد') ?>
                    </a>
                    <a href="<?= base_url('inventory') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للمخزون') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام الديناميكي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <div>
                        <strong>النظام الديناميكي نشط!</strong>
                        <p class="mb-0">يتم عرض الحقول المخصصة لشركتك فقط. 
                        عدد الحقول المفعلة: <strong><?= count($visibleFields) ?></strong> حقل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i> <?= __('إحصائيات المنتجات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-primary mb-2">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count($products) ?></h3>
                                    <p class="text-muted mb-0"><?= __('إجمالي المنتجات') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-success mb-2">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count(array_filter($products, fn($p) => ($p['is_active'] ?? 1) == 1)) ?></h3>
                                    <p class="text-muted mb-0"><?= __('منتجات نشطة') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-info mb-2">
                                        <i class="fas fa-cogs fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count($visibleFields) ?></h3>
                                    <p class="text-muted mb-0"><?= __('حقول مفعلة') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="text-warning mb-2">
                                        <i class="fas fa-layer-group fa-2x"></i>
                                    </div>
                                    <h3 class="mb-1"><?= count($fieldGroups) ?></h3>
                                    <p class="text-muted mb-0"><?= __('مجموعات الحقول') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات الديناميكي -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i> <?= __('قائمة المنتجات') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($products)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات</h5>
                            <p class="text-muted">ابدأ بإضافة منتجات جديدة باستخدام النظام الديناميكي</p>
                            <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة أول منتج
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table id="dynamicProductsTable" class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <?php 
                                        // عرض الحقول المرئية كعناوين أعمدة
                                        $displayFields = array_filter($visibleFields, function($field) {
                                            return $field['is_listable'] == 1 && $field['input_type'] != 'hidden';
                                        });
                                        
                                        // ترتيب الحقول حسب الأولوية
                                        usort($displayFields, function($a, $b) {
                                            return ($a['company_order'] ?? $a['display_order']) - ($b['company_order'] ?? $b['display_order']);
                                        });
                                        ?>
                                        
                                        <?php foreach ($displayFields as $field): ?>
                                            <th><?= htmlspecialchars($field['custom_label_ar'] ?: $field['field_name_ar']) ?></th>
                                        <?php endforeach; ?>
                                        
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <?php foreach ($displayFields as $field): ?>
                                                <td>
                                                    <?php 
                                                    $fieldCode = $field['field_code'];
                                                    $value = $product[$fieldCode] ?? '';
                                                    
                                                    // تنسيق القيم حسب نوع الحقل
                                                    switch ($field['input_type']) {
                                                        case 'number':
                                                            if ($fieldCode == 'cost_price' || $fieldCode == 'selling_price') {
                                                                echo number_format((float)$value, 2) . ' ر.س';
                                                            } else {
                                                                echo number_format((float)$value, 2);
                                                            }
                                                            break;
                                                            
                                                        case 'checkbox':
                                                            if ($value) {
                                                                echo '<span class="badge badge-success">نعم</span>';
                                                            } else {
                                                                echo '<span class="badge badge-secondary">لا</span>';
                                                            }
                                                            break;
                                                            
                                                        case 'date':
                                                            if ($value) {
                                                                echo date('Y-m-d', strtotime($value));
                                                            }
                                                            break;
                                                            
                                                        case 'select':
                                                            // عرض القيم المترجمة للحقول المرتبطة
                                                            if ($fieldCode == 'category_id' && isset($product['category_name_ar'])) {
                                                                echo htmlspecialchars($product['category_name_ar']);
                                                            } elseif ($fieldCode == 'unit_id' && isset($product['unit_name_ar'])) {
                                                                echo htmlspecialchars($product['unit_name_ar']);
                                                            } else {
                                                                echo htmlspecialchars($value);
                                                            }
                                                            break;
                                                            
                                                        case 'file':
                                                            if ($value) {
                                                                echo '<img src="' . base_url($value) . '" alt="صورة" class="img-thumbnail" style="max-width: 50px; max-height: 50px;">';
                                                            } else {
                                                                echo '<i class="fas fa-image text-muted"></i>';
                                                            }
                                                            break;
                                                            
                                                        default:
                                                            // تحديد طول النص المعروض
                                                            $maxLength = 50;
                                                            if (strlen($value) > $maxLength) {
                                                                echo '<span title="' . htmlspecialchars($value) . '">' . 
                                                                     htmlspecialchars(substr($value, 0, $maxLength)) . '...</span>';
                                                            } else {
                                                                echo htmlspecialchars($value);
                                                            }
                                                            break;
                                                    }
                                                    ?>
                                                </td>
                                            <?php endforeach; ?>
                                            
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>"
                                                       class="btn btn-outline-info btn-sm" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>"
                                                       class="btn btn-outline-warning btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteProduct(<?= $product['product_id'] ?>)"
                                                            class="btn btn-outline-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة DataTables
    $('#dynamicProductsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        pageLength: 25,
        responsive: true,
        order: [[1, 'asc']], // ترتيب حسب أول عمود بيانات
        columnDefs: [
            { orderable: false, targets: [-1] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
});

// حذف منتج
function deleteProduct(productId) {
    if (confirm('<?= __('هل أنت متأكد من حذف هذا المنتج؟') ?>')) {
        fetch(`<?= base_url('inventory/products/') ?>${productId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '<?= __('حدث خطأ أثناء حذف المنتج') ?>');
            }
        })
        .catch(error => {
            alert('<?= __('حدث خطأ أثناء حذف المنتج') ?>');
        });
    }
}
</script>

<style>
.badge {
    font-size: 0.75em;
}

.img-thumbnail {
    border-radius: 4px;
}

.btn-group .btn {
    margin-right: 2px;
}

.table th {
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* تحسين عرض الجدول على الشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
