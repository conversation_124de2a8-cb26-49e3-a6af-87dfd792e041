<?php
namespace App\Core;

use PDO;
use Exception;

/**
 * مدير الحقول الديناميكية
 * يدير الحقول والتبويبات للشركات
 */
class FieldManager
{
    private $db;
    
    public function __construct($database)
    {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع الحقول المتاحة لوحدة معينة
     */
    public function getSystemFields($moduleCode, $tableName)
    {
        $sql = "SELECT sf.*, st.tab_name, st.tab_label_ar, st.tab_label_en, st.tab_icon, st.tab_color, st.display_order as tab_order
                FROM system_fields sf
                LEFT JOIN system_tabs st ON sf.tab_id = st.tab_id
                WHERE sf.module_name = ? AND sf.table_name = ?
                ORDER BY st.display_order, sf.display_order";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$moduleCode, $tableName]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على الحقول المختارة للشركة
     */
    public function getCompanySelectedFields($companyId, $moduleCode, $tableName)
    {
        $sql = "SELECT sf.*, st.tab_name, st.tab_label_ar, st.tab_label_en, st.tab_icon, st.tab_color, st.display_order as tab_order,
                       cfs.is_enabled, cfs.is_visible_form, cfs.is_visible_table, cfs.is_visible_details, cfs.is_hidden,
                       cfs.is_required, cfs.is_searchable, cfs.is_filterable, cfs.is_sortable, cfs.is_exportable,
                       cfs.custom_label_ar, cfs.custom_label_en, cfs.custom_placeholder_ar, cfs.custom_placeholder_en,
                       cfs.custom_help_text_ar, cfs.custom_help_text_en, cfs.custom_validation_rules, cfs.custom_options,
                       cfs.display_order_form, cfs.display_order_table, cfs.column_width, cfs.field_group
                FROM system_fields sf
                LEFT JOIN system_tabs st ON sf.tab_id = st.tab_id
                INNER JOIN company_field_selections cfs ON sf.field_id = cfs.field_id
                WHERE sf.module_name = ? AND sf.table_name = ? AND cfs.company_id = ? AND cfs.is_enabled = 1
                ORDER BY st.display_order, cfs.display_order_form";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$moduleCode, $tableName, $companyId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على الحقول المرئية في النماذج
     */
    public function getCompanyFormFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_visible_form' => 1,
            'is_hidden' => 0
        ], 'display_order_form');
    }
    
    /**
     * الحصول على الحقول المرئية في الجداول
     */
    public function getCompanyTableFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_visible_table' => 1
        ], 'display_order_table');
    }
    
    /**
     * الحصول على الحقول المرئية في صفحة التفاصيل
     */
    public function getCompanyDetailFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_visible_details' => 1
        ], 'display_order_form');
    }
    
    /**
     * الحصول على الحقول المطلوبة
     */
    public function getCompanyRequiredFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_required' => 1
        ]);
    }
    
    /**
     * الحصول على الحقول القابلة للبحث
     */
    public function getCompanySearchableFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_searchable' => 1
        ]);
    }
    
    /**
     * الحصول على الحقول القابلة للفلترة
     */
    public function getCompanyFilterableFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_filterable' => 1
        ]);
    }
    
    /**
     * الحصول على الحقول المخفية
     */
    public function getCompanyHiddenFields($companyId, $moduleCode, $tableName)
    {
        return $this->getFieldsByCondition($companyId, $moduleCode, $tableName, [
            'is_enabled' => 1,
            'is_hidden' => 1
        ]);
    }
    
    /**
     * تجميع الحقول حسب التبويبات (التبويبات تظهر تلقائياً)
     */
    public function getCompanyFieldsGroupedByTabs($companyId, $moduleCode, $tableName, $fieldType = 'form')
    {
        // الحصول على الحقول المناسبة
        switch($fieldType) {
            case 'form':
                $fields = $this->getCompanyFormFields($companyId, $moduleCode, $tableName);
                break;
            case 'table':
                $fields = $this->getCompanyTableFields($companyId, $moduleCode, $tableName);
                break;
            case 'details':
                $fields = $this->getCompanyDetailFields($companyId, $moduleCode, $tableName);
                break;
            default:
                $fields = $this->getCompanySelectedFields($companyId, $moduleCode, $tableName);
        }
        
        // تجميع الحقول حسب التبويبات
        $tabs = [];
        foreach($fields as $field) {
            $tabId = $field['tab_id'];
            
            if (!isset($tabs[$tabId])) {
                $tabs[$tabId] = [
                    'tab_id' => $tabId,
                    'tab_name' => $field['tab_name'],
                    'tab_label_ar' => $field['tab_label_ar'],
                    'tab_label_en' => $field['tab_label_en'],
                    'tab_icon' => $field['tab_icon'],
                    'tab_color' => $field['tab_color'],
                    'tab_order' => $field['tab_order'],
                    'fields' => []
                ];
            }
            
            $tabs[$tabId]['fields'][] = $field;
        }
        
        // ترتيب التبويبات حسب ترتيب النظام
        uasort($tabs, function($a, $b) {
            return $a['tab_order'] - $b['tab_order'];
        });
        
        return $tabs;
    }
    
    /**
     * الحصول على الحقول بشروط معينة
     */
    private function getFieldsByCondition($companyId, $moduleCode, $tableName, $conditions, $orderBy = 'display_order_form')
    {
        $whereConditions = [];
        $params = [$moduleCode, $tableName, $companyId];
        
        foreach($conditions as $field => $value) {
            $whereConditions[] = "cfs.{$field} = ?";
            $params[] = $value;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $sql = "SELECT sf.*, st.tab_name, st.tab_label_ar, st.tab_label_en, st.tab_icon, st.tab_color, st.display_order as tab_order,
                       cfs.is_enabled, cfs.is_visible_form, cfs.is_visible_table, cfs.is_visible_details, cfs.is_hidden,
                       cfs.is_required, cfs.is_searchable, cfs.is_filterable, cfs.is_sortable, cfs.is_exportable,
                       cfs.custom_label_ar, cfs.custom_label_en, cfs.custom_placeholder_ar, cfs.custom_placeholder_en,
                       cfs.custom_help_text_ar, cfs.custom_help_text_en, cfs.custom_validation_rules, cfs.custom_options,
                       cfs.display_order_form, cfs.display_order_table, cfs.column_width, cfs.field_group
                FROM system_fields sf
                LEFT JOIN system_tabs st ON sf.tab_id = st.tab_id
                INNER JOIN company_field_selections cfs ON sf.field_id = cfs.field_id
                WHERE sf.module_name = ? AND sf.table_name = ? AND cfs.company_id = ? AND {$whereClause}
                ORDER BY cfs.{$orderBy}";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * حفظ قيمة حقل ديناميكي
     */
    public function saveFieldValue($companyId, $moduleCode, $tableName, $recordId, $fieldId, $value, $userId)
    {
        // التحقق من وجود القيمة
        $sql = "SELECT id FROM dynamic_field_values 
                WHERE company_id = ? AND module_name = ? AND table_name = ? AND record_id = ? AND field_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $moduleCode, $tableName, $recordId, $fieldId]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            // تحديث القيمة الموجودة
            $sql = "UPDATE dynamic_field_values 
                    SET field_value = ?, updated_by = ?, updated_at = NOW()
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$value, $userId, $existing['id']]);
        } else {
            // إدراج قيمة جديدة
            $sql = "INSERT INTO dynamic_field_values 
                    (company_id, module_name, table_name, record_id, field_id, field_value, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$companyId, $moduleCode, $tableName, $recordId, $fieldId, $value, $userId]);
        }
    }
    
    /**
     * الحصول على قيم الحقول لسجل معين
     */
    public function getFieldValues($companyId, $moduleCode, $tableName, $recordId)
    {
        $sql = "SELECT dfv.field_id, dfv.field_value, sf.field_name
                FROM dynamic_field_values dfv
                INNER JOIN system_fields sf ON dfv.field_id = sf.field_id
                WHERE dfv.company_id = ? AND dfv.module_name = ? AND dfv.table_name = ? AND dfv.record_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $moduleCode, $tableName, $recordId]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحويل إلى مصفوفة مفاتيح
        $values = [];
        foreach($results as $row) {
            $values[$row['field_name']] = $row['field_value'];
        }
        
        return $values;
    }
    
    /**
     * التحقق من وجود إعدادات للشركة
     */
    public function hasCompanyFieldSettings($companyId, $moduleCode, $tableName)
    {
        $sql = "SELECT COUNT(*) FROM company_field_selections cfs
                INNER JOIN system_fields sf ON cfs.field_id = sf.field_id
                WHERE cfs.company_id = ? AND sf.module_name = ? AND sf.table_name = ? AND cfs.is_enabled = 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$companyId, $moduleCode, $tableName]);
        
        return $stmt->fetchColumn() > 0;
    }
}
