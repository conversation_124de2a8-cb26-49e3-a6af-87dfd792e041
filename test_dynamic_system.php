<?php
/**
 * اختبار النظام الديناميكي للجداول
 * هذا الملف لاختبار النظام الديناميكي قبل التطبيق الكامل
 */

// تحديد المسار الأساسي
define('BASE_PATH', __DIR__ . '/erpapp');

// تحميل الإعدادات
require_once BASE_PATH . '/config/config.php';
require_once BASE_PATH . '/config/database.php';
require_once BASE_PATH . '/App/Helpers/functions.php';

// الاتصال بقاعدة البيانات
try {
    $db = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
} catch (PDOException $e) {
    die("❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n");
}

echo "\n=== اختبار النظام الديناميكي للجداول ===\n\n";

// 1. قراءة ملف SQL وتنفيذه
echo "1. إنشاء جداول النظام الديناميكي...\n";
$sqlFile = __DIR__ . '/dynamic_system_setup.sql';

if (!file_exists($sqlFile)) {
    die("❌ ملف SQL غير موجود: {$sqlFile}\n");
}

$sql = file_get_contents($sqlFile);
$statements = explode(';', $sql);

$successCount = 0;
$errorCount = 0;

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (empty($statement)) continue;
    
    try {
        $db->exec($statement);
        $successCount++;
    } catch (PDOException $e) {
        $errorCount++;
        echo "⚠️  خطأ في تنفيذ: " . substr($statement, 0, 50) . "...\n";
        echo "   الخطأ: " . $e->getMessage() . "\n";
    }
}

echo "✅ تم تنفيذ {$successCount} استعلام بنجاح\n";
if ($errorCount > 0) {
    echo "⚠️  {$errorCount} استعلام فشل\n";
}

// 2. اختبار فئة DynamicTableManager
echo "\n2. اختبار فئة DynamicTableManager...\n";

require_once BASE_PATH . '/App/Core/DynamicTableManager.php';

try {
    $dynamicManager = new \App\Core\DynamicTableManager($db);
    echo "✅ تم إنشاء DynamicTableManager بنجاح\n";
    
    // اختبار الحصول على الحقول المتاحة
    $availableFields = $dynamicManager->getAvailableFields('inventory', 'product');
    echo "✅ تم الحصول على " . count($availableFields) . " حقل متاح\n";
    
    // عرض بعض الحقول
    echo "\nالحقول المتاحة:\n";
    foreach (array_slice($availableFields, 0, 5) as $field) {
        echo "  - {$field['field_name_ar']} ({$field['field_code']}) - {$field['category']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار DynamicTableManager: " . $e->getMessage() . "\n";
}

// 3. اختبار إنشاء إعدادات افتراضية لشركة تجريبية
echo "\n3. اختبار إنشاء إعدادات افتراضية...\n";

$testCompanyId = 4; // شركة موجودة في قاعدة البيانات
$testUserId = 32;   // مستخدم موجود

try {
    $fieldsCount = $dynamicManager->createDefaultSettingsForCompany($testCompanyId, $testUserId, 'inventory', 'product');
    echo "✅ تم إنشاء إعدادات افتراضية: {$fieldsCount} حقل\n";
    
    // التحقق من الحقول المفعلة
    $enabledFields = $dynamicManager->getCompanyEnabledFields($testCompanyId, 'inventory', 'product');
    echo "✅ الحقول المفعلة للشركة: " . count($enabledFields) . " حقل\n";
    
    echo "\nالحقول المفعلة:\n";
    foreach (array_slice($enabledFields, 0, 8) as $field) {
        $required = $field['is_required'] ? ' (مطلوب)' : '';
        echo "  - {$field['field_name_ar']}{$required}\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الإعدادات الافتراضية: " . $e->getMessage() . "\n";
}

// 4. اختبار إنشاء الجدول الديناميكي
echo "\n4. اختبار إنشاء الجدول الديناميكي...\n";

try {
    $tableName = $dynamicManager->createCompanyTable($testCompanyId, 'inventory', 'product', $testUserId);
    echo "✅ تم إنشاء الجدول: {$tableName}\n";
    
    // التحقق من وجود الجدول
    $checkTable = $db->query("SHOW TABLES LIKE '{$tableName}'")->fetch();
    if ($checkTable) {
        echo "✅ تم التحقق من وجود الجدول في قاعدة البيانات\n";
        
        // عرض هيكل الجدول
        $columns = $db->query("DESCRIBE {$tableName}")->fetchAll();
        echo "\nهيكل الجدول ({$tableName}):\n";
        foreach (array_slice($columns, 0, 10) as $column) {
            $null = $column['Null'] == 'YES' ? 'NULL' : 'NOT NULL';
            echo "  - {$column['Field']}: {$column['Type']} {$null}\n";
        }
        if (count($columns) > 10) {
            echo "  ... و " . (count($columns) - 10) . " حقل آخر\n";
        }
        
    } else {
        echo "❌ الجدول غير موجود في قاعدة البيانات\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "\n";
}

// 5. اختبار إضافة بيانات تجريبية
echo "\n5. اختبار إضافة بيانات تجريبية...\n";

try {
    if (isset($tableName)) {
        // إضافة منتج تجريبي
        $insertSQL = "INSERT INTO {$tableName} 
                      (company_id, product_code, product_name_ar, category_id, unit_id, 
                       cost_price, selling_price, is_active, created_by, created_at) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $db->prepare($insertSQL);
        $result = $stmt->execute([
            $testCompanyId,
            'DYNAMIC_TEST_001',
            'منتج تجريبي ديناميكي',
            2, // فئة موجودة
            7, // وحدة موجودة
            100.00,
            150.00,
            1,
            $testUserId
        ]);
        
        if ($result) {
            $productId = $db->lastInsertId();
            echo "✅ تم إضافة منتج تجريبي برقم: {$productId}\n";
            
            // قراءة المنتج
            $selectSQL = "SELECT product_id, product_code, product_name_ar, cost_price, selling_price 
                          FROM {$tableName} WHERE product_id = ?";
            $stmt = $db->prepare($selectSQL);
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
            if ($product) {
                echo "✅ تم قراءة المنتج: {$product['product_name_ar']} - {$product['product_code']}\n";
                echo "   السعر: {$product['cost_price']} -> {$product['selling_price']}\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "\n";
}

// 6. اختبار تعديل إعدادات الحقول
echo "\n6. اختبار تعديل إعدادات الحقول...\n";

try {
    // تفعيل حقول إضافية
    $additionalFields = [
        'color' => ['is_visible' => 1, 'display_order' => 20],
        'weight' => ['is_visible' => 1, 'display_order' => 21],
        'warranty_period' => ['is_required' => 1, 'display_order' => 22]
    ];
    
    $addedCount = 0;
    foreach ($additionalFields as $fieldCode => $options) {
        // البحث عن الحقل
        $fieldSQL = "SELECT field_id FROM field_library WHERE field_code = ? AND module_code = 'inventory'";
        $stmt = $db->prepare($fieldSQL);
        $stmt->execute([$fieldCode]);
        $field = $stmt->fetch();
        
        if ($field) {
            $dynamicManager->toggleFieldForCompany($testCompanyId, $field['field_id'], true, $testUserId, $options);
            $addedCount++;
        }
    }
    
    echo "✅ تم تفعيل {$addedCount} حقل إضافي\n";
    
    // إعادة إنشاء الجدول بالحقول الجديدة
    $newTableName = $dynamicManager->createCompanyTable($testCompanyId, 'inventory', 'product', $testUserId);
    echo "✅ تم إعادة إنشاء الجدول مع الحقول الجديدة\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تعديل إعدادات الحقول: " . $e->getMessage() . "\n";
}

// 7. ملخص النتائج
echo "\n=== ملخص الاختبار ===\n";
echo "✅ النظام الديناميكي يعمل بنجاح!\n";
echo "✅ يمكن إنشاء جداول مخصصة لكل شركة\n";
echo "✅ يمكن تخصيص الحقول حسب احتياجات كل شركة\n";
echo "✅ يمكن إضافة وتعديل البيانات في الجداول الديناميكية\n";
echo "\n🎯 النظام جاهز للتطبيق!\n";

echo "\n=== الخطوات التالية ===\n";
echo "1. تشغيل الواجهة: /inventory/settings/fields\n";
echo "2. اختبار إنشاء إعدادات افتراضية\n";
echo "3. اختبار إنشاء الجدول الديناميكي\n";
echo "4. تطوير نماذج إدخال ديناميكية\n";
echo "5. تحديث ProductModel للعمل مع الجداول الديناميكية\n";

?>
