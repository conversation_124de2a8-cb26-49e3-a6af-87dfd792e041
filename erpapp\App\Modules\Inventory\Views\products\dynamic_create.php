<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إضافة منتج جديد') ?>
                    <span class="badge badge-success">ديناميكي</span>
                </h1>
                <div>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة للقائمة') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام الديناميكي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success">
                <div class="d-flex align-items-center">
                    <i class="fas fa-magic me-2"></i>
                    <div>
                        <strong>نموذج ديناميكي!</strong>
                        <p class="mb-0">يتم عرض الحقول المخصصة لشركتك فقط. 
                        عدد الحقول المرئية: <strong><?= count($visibleFields) ?></strong> حقل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة المنتج -->
    <form id="productForm" method="POST" action="<?= base_url('inventory/products/store') ?>" enctype="multipart/form-data">
        
        <?php 
        // تجميع الحقول حسب المجموعة
        $categoryNames = [
            'core' => 'الحقول الأساسية',
            'basic' => 'المعلومات الأساسية', 
            'classification' => 'التصنيف',
            'pricing' => 'الأسعار',
            'inventory' => 'المخزون',
            'physical' => 'الخصائص الفيزيائية',
            'warranty' => 'الضمان',
            'dates' => 'التواريخ',
            'supplier' => 'معلومات المورد',
            'shipping' => 'الشحن',
            'media' => 'الوسائط',
            'tax' => 'الضرائب'
        ];
        ?>

        <?php foreach ($fieldGroups as $groupName => $groupFields): ?>
            <?php if ($groupName == 'core') continue; // تخطي الحقول الأساسية المخفية ?>
            
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-folder me-2"></i>
                                <?= $categoryNames[$groupName] ?? $groupName ?>
                                <span class="badge badge-light ms-2"><?= count($groupFields) ?> حقل</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($groupFields as $field): ?>
                                    <?php 
                                    $fieldCode = $field['field_code'];
                                    $isRequired = in_array($fieldCode, $requiredFields);
                                    $fieldLabel = $field['custom_label_ar'] ?: $field['field_name_ar'];
                                    ?>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="<?= $fieldCode ?>" class="form-label">
                                            <?= htmlspecialchars($fieldLabel) ?>
                                            <?php if ($isRequired): ?>
                                                <span class="text-danger">*</span>
                                            <?php endif; ?>
                                        </label>
                                        
                                        <?php if ($field['description_ar']): ?>
                                            <small class="form-text text-muted d-block">
                                                <?= htmlspecialchars($field['description_ar']) ?>
                                            </small>
                                        <?php endif; ?>
                                        
                                        <?php
                                        // إنشاء الحقل حسب النوع
                                        switch ($field['input_type']):
                                            case 'text':
                                            case 'number':
                                                $inputType = $field['input_type'];
                                                $step = '';
                                                $min = '';
                                                
                                                if ($inputType == 'number') {
                                                    if (strpos($field['mysql_type'], 'DECIMAL') !== false) {
                                                        $step = 'step="0.01"';
                                                    }
                                                    if ($field['validation_rules']) {
                                                        $rules = json_decode($field['validation_rules'], true);
                                                        if (isset($rules['min'])) {
                                                            $min = 'min="' . $rules['min'] . '"';
                                                        }
                                                    }
                                                }
                                                ?>
                                                <input type="<?= $inputType ?>" 
                                                       id="<?= $fieldCode ?>" 
                                                       name="<?= $fieldCode ?>" 
                                                       class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                       value="<?= htmlspecialchars($field['default_value'] ?? '') ?>"
                                                       <?= $isRequired ? 'required' : '' ?>
                                                       <?= $step ?>
                                                       <?= $min ?>
                                                       placeholder="<?= htmlspecialchars($fieldLabel) ?>">
                                                <?php
                                                break;
                                                
                                            case 'textarea':
                                                ?>
                                                <textarea id="<?= $fieldCode ?>" 
                                                          name="<?= $fieldCode ?>" 
                                                          class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                          rows="3"
                                                          <?= $isRequired ? 'required' : '' ?>
                                                          placeholder="<?= htmlspecialchars($fieldLabel) ?>"><?= htmlspecialchars($field['default_value'] ?? '') ?></textarea>
                                                <?php
                                                break;
                                                
                                            case 'select':
                                                ?>
                                                <select id="<?= $fieldCode ?>" 
                                                        name="<?= $fieldCode ?>" 
                                                        class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                        <?= $isRequired ? 'required' : '' ?>>
                                                    <option value="">اختر <?= htmlspecialchars($fieldLabel) ?></option>
                                                    
                                                    <?php
                                                    // خيارات خاصة للحقول المرتبطة
                                                    if ($fieldCode == 'category_id' && !empty($categories)):
                                                        foreach ($categories as $category):
                                                            ?>
                                                            <option value="<?= $category['category_id'] ?>">
                                                                <?= htmlspecialchars($category['category_name_ar']) ?>
                                                            </option>
                                                            <?php
                                                        endforeach;
                                                    elseif ($fieldCode == 'unit_id' && !empty($units)):
                                                        foreach ($units as $unit):
                                                            ?>
                                                            <option value="<?= $unit['unit_id'] ?>">
                                                                <?= htmlspecialchars($unit['unit_name_ar']) ?>
                                                                (<?= htmlspecialchars($unit['unit_symbol_ar']) ?>)
                                                            </option>
                                                            <?php
                                                        endforeach;
                                                    elseif ($field['field_options']):
                                                        $options = json_decode($field['field_options'], true);
                                                        if (is_array($options)):
                                                            foreach ($options as $option):
                                                                ?>
                                                                <option value="<?= htmlspecialchars($option) ?>">
                                                                    <?= htmlspecialchars($option) ?>
                                                                </option>
                                                                <?php
                                                            endforeach;
                                                        endif;
                                                    endif;
                                                    ?>
                                                </select>
                                                <?php
                                                break;
                                                
                                            case 'checkbox':
                                                ?>
                                                <div class="form-check">
                                                    <input type="checkbox" 
                                                           id="<?= $fieldCode ?>" 
                                                           name="<?= $fieldCode ?>" 
                                                           value="1"
                                                           class="form-check-input"
                                                           <?= ($field['default_value'] == '1') ? 'checked' : '' ?>>
                                                    <label class="form-check-label" for="<?= $fieldCode ?>">
                                                        <?= htmlspecialchars($fieldLabel) ?>
                                                    </label>
                                                </div>
                                                <?php
                                                break;
                                                
                                            case 'date':
                                                ?>
                                                <input type="date" 
                                                       id="<?= $fieldCode ?>" 
                                                       name="<?= $fieldCode ?>" 
                                                       class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                       value="<?= htmlspecialchars($field['default_value'] ?? '') ?>"
                                                       <?= $isRequired ? 'required' : '' ?>>
                                                <?php
                                                break;
                                                
                                            case 'file':
                                                ?>
                                                <input type="file" 
                                                       id="<?= $fieldCode ?>" 
                                                       name="<?= $fieldCode ?>" 
                                                       class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                       accept="image/*"
                                                       <?= $isRequired ? 'required' : '' ?>>
                                                <small class="form-text text-muted">
                                                    الملفات المدعومة: JPG, PNG, GIF (حد أقصى 2MB)
                                                </small>
                                                <?php
                                                break;
                                                
                                            default:
                                                ?>
                                                <input type="text" 
                                                       id="<?= $fieldCode ?>" 
                                                       name="<?= $fieldCode ?>" 
                                                       class="form-control <?= $isRequired ? 'required' : '' ?>"
                                                       value="<?= htmlspecialchars($field['default_value'] ?? '') ?>"
                                                       <?= $isRequired ? 'required' : '' ?>
                                                       placeholder="<?= htmlspecialchars($fieldLabel) ?>">
                                                <?php
                                                break;
                                        endswitch;
                                        ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- أزرار الحفظ -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-save me-2"></i>
                            <?= __('حفظ المنتج') ?>
                        </button>
                        <button type="submit" name="save_and_new" value="1" class="btn btn-primary btn-lg me-2">
                            <i class="fas fa-plus me-2"></i>
                            <?= __('حفظ وإضافة آخر') ?>
                        </button>
                        <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>
                            <?= __('إلغاء') ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // تحسين تجربة المستخدم
    
    // التحقق من صحة النموذج
    $('#productForm').on('submit', function(e) {
        let isValid = true;
        let firstError = null;
        
        // التحقق من الحقول المطلوبة
        $('.required').each(function() {
            if (!$(this).val().trim()) {
                isValid = false;
                $(this).addClass('is-invalid');
                if (!firstError) {
                    firstError = $(this);
                }
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            if (firstError) {
                firstError.focus();
                // التمرير إلى الحقل الأول الخاطئ
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    
    // إزالة رسالة الخطأ عند الكتابة
    $('.required').on('input change', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid');
        }
    });
    
    // تحسين حقول الأرقام
    $('input[type="number"]').on('input', function() {
        let value = parseFloat($(this).val());
        let min = parseFloat($(this).attr('min'));
        
        if (!isNaN(min) && value < min) {
            $(this).val(min);
        }
    });
    
    // معاينة الصور
    $('input[type="file"]').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // إنشاء معاينة للصورة
                let preview = $(this).siblings('.image-preview');
                if (preview.length === 0) {
                    preview = $('<div class="image-preview mt-2"></div>');
                    $(this).after(preview);
                }
                preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">');
            }.bind(this);
            reader.readAsDataURL(file);
        }
    });
});
</script>

<style>
.is-invalid {
    border-color: #dc3545;
}

.form-check {
    padding-top: 0.375rem;
}

.image-preview img {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

.badge {
    font-size: 0.75em;
}

/* تحسين عرض النموذج على الشاشات الصغيرة */
@media (max-width: 768px) {
    .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
}
</style>
