<?php
/**
 * System Health Check
 * فحص صحة النظام والتأكد من أن كل شيء يعمل بشكل صحيح
 */

// تحديد المسار الأساسي
define('BASE_PATH', __DIR__ . '/erpapp');

// تحميل Composer autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    echo "✅ Composer autoloader loaded successfully\n";
} else {
    echo "❌ Composer autoloader not found\n";
    exit(1);
}

// تحميل الإعدادات
if (file_exists(BASE_PATH . '/config/config.php')) {
    require_once BASE_PATH . '/config/config.php';
    echo "✅ Configuration loaded successfully\n";
} else {
    echo "❌ Configuration file not found\n";
    exit(1);
}

// فحص ملف .env
if (file_exists(BASE_PATH . '/.env')) {
    echo "✅ .env file exists\n";
} else {
    echo "❌ .env file not found\n";
}

// فحص قاعدة البيانات
if (file_exists(BASE_PATH . '/config/database.php')) {
    require_once BASE_PATH . '/config/database.php';
    echo "✅ Database configuration loaded\n";
} else {
    echo "❌ Database configuration not found\n";
}

// فحص الكلاسات الأساسية
$coreClasses = [
    'App\Core\Module',
    'App\Core\ModuleRouter', 
    'App\Core\Router',
    'App\Core\ExceptionHandler',
    'App\Helpers\PermissionManager'
];

echo "\n🔍 Checking Core Classes:\n";
foreach ($coreClasses as $class) {
    if (class_exists($class)) {
        echo "✅ $class - OK\n";
    } else {
        echo "❌ $class - NOT FOUND\n";
    }
}

// فحص المجلدات المطلوبة
$requiredDirs = [
    'erpapp/storage/logs',
    'erpapp/storage/cache', 
    'erpapp/storage/sessions',
    'erpapp/public/uploads',
    'vendor'
];

echo "\n📁 Checking Required Directories:\n";
foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ $dir - EXISTS\n";
    } else {
        echo "❌ $dir - NOT FOUND\n";
    }
}

// فحص الحزم المثبتة
echo "\n📦 Checking Installed Packages:\n";
$packages = [
    'PHPMailer\PHPMailer\PHPMailer',
    'Monolog\Logger',
    'Dotenv\Dotenv'
];

foreach ($packages as $package) {
    if (class_exists($package)) {
        echo "✅ $package - INSTALLED\n";
    } else {
        echo "❌ $package - NOT INSTALLED\n";
    }
}

// فحص إصدار PHP
echo "\n🐘 PHP Information:\n";
echo "✅ PHP Version: " . PHP_VERSION . "\n";
echo "✅ Memory Limit: " . ini_get('memory_limit') . "\n";
echo "✅ Max Execution Time: " . ini_get('max_execution_time') . "s\n";

// فحص الامتدادات المطلوبة
$requiredExtensions = ['pdo', 'json', 'mbstring', 'openssl', 'ctype'];
echo "\n🔧 Checking PHP Extensions:\n";
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext - LOADED\n";
    } else {
        echo "❌ $ext - NOT LOADED\n";
    }
}

echo "\n🎉 System check completed!\n";
echo "📊 Overall Status: ";

// تحديد الحالة العامة
$errors = substr_count(ob_get_contents(), '❌');
if ($errors === 0) {
    echo "🟢 EXCELLENT - All systems operational!\n";
} elseif ($errors <= 2) {
    echo "🟡 GOOD - Minor issues detected\n";
} else {
    echo "🔴 NEEDS ATTENTION - Multiple issues found\n";
}
