-- ========================================
-- نظام الفئات الديناميكي
-- يتيح لكل شركة إنشاء فئات مخصصة للمنتجات
-- ========================================

-- ========================================
-- 1. جدول تبويبات الفئات
-- ========================================
INSERT INTO `system_tabs` (`module_name`, `table_name`, `tab_name`, `tab_label_ar`, `tab_label_en`, `tab_description_ar`, `tab_description_en`, `tab_icon`, `tab_color`, `display_order`, `is_default_enabled`, `is_core_tab`) VALUES

-- التبويبات الأساسية للفئات
('inventory', 'categories', 'basic_info', 'المعلومات الأساسية', 'Basic Information', 'المعلومات الأساسية للفئة مثل الاسم والكود', 'Basic category information like name and code', 'fas fa-info-circle', '#007bff', 1, 1, 1),
('inventory', 'categories', 'hierarchy', 'التسلسل الهرمي', 'Hierarchy', 'إعدادات التسلسل الهرمي والفئة الأب', 'Hierarchy settings and parent category', 'fas fa-sitemap', '#28a745', 2, 1, 1),
('inventory', 'categories', 'display', 'العرض والتصميم', 'Display & Design', 'إعدادات العرض والألوان والأيقونات', 'Display settings, colors and icons', 'fas fa-palette', '#ffc107', 3, 1, 0),
('inventory', 'categories', 'seo', 'تحسين محركات البحث', 'SEO', 'إعدادات تحسين محركات البحث', 'Search engine optimization settings', 'fas fa-search', '#17a2b8', 4, 0, 0),
('inventory', 'categories', 'settings', 'الإعدادات المتقدمة', 'Advanced Settings', 'الإعدادات المتقدمة والخصائص الإضافية', 'Advanced settings and additional properties', 'fas fa-cogs', '#6f42c1', 5, 0, 0);

-- ========================================
-- 2. حقول الفئات
-- ========================================
INSERT INTO `system_fields` (`module_name`, `table_name`, `tab_id`, `field_name`, `field_label_ar`, `field_label_en`, `field_description_ar`, `field_description_en`, `field_type`, `field_length`, `input_type`, `validation_rules`, `field_options`, `default_value`, `placeholder_ar`, `placeholder_en`, `help_text_ar`, `help_text_en`, `is_required_default`, `is_core_field`, `is_system_field`, `display_order`) VALUES

-- الحقول الأساسية (تبويب المعلومات الأساسية)
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'category_id', 'معرف الفئة', 'Category ID', 'المعرف الفريد للفئة', 'Unique category identifier', 'INT', NULL, 'hidden', '{"auto_increment": true}', NULL, NULL, NULL, NULL, 'معرف تلقائي للفئة', 'Auto-generated category ID', 0, 1, 1, 1),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'company_id', 'معرف الشركة', 'Company ID', 'معرف الشركة المالكة للفئة', 'Company ID that owns the category', 'INT', NULL, 'hidden', '{"required": true}', NULL, NULL, NULL, NULL, 'معرف الشركة المالكة', 'Owner company identifier', 1, 1, 1, 2),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'category_code', 'كود الفئة', 'Category Code', 'كود الفئة الفريد داخل الشركة', 'Unique category code within company', 'VARCHAR', '50', 'text', '{"required": true, "unique": true, "max_length": 50}', NULL, NULL, 'أدخل كود الفئة', 'Enter category code', 'كود فريد للفئة لا يتكرر', 'Unique category code', 1, 1, 0, 3),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'category_name_ar', 'اسم الفئة', 'Category Name (Arabic)', 'اسم الفئة باللغة العربية', 'Category name in Arabic', 'VARCHAR', '200', 'text', '{"required": true, "max_length": 200}', NULL, NULL, 'أدخل اسم الفئة', 'Enter category name', 'اسم الفئة كما سيظهر في النظام', 'Category name as it will appear in system', 1, 1, 0, 4),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'category_name_en', 'الاسم بالإنجليزية', 'Category Name (English)', 'اسم الفئة باللغة الإنجليزية', 'Category name in English', 'VARCHAR', '200', 'text', '{"max_length": 200}', NULL, NULL, 'أدخل الاسم بالإنجليزية', 'Enter English name', 'اسم الفئة باللغة الإنجليزية (اختياري)', 'Category name in English (optional)', 0, 0, 0, 5),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'description_ar', 'الوصف', 'Description (Arabic)', 'وصف تفصيلي للفئة بالعربية', 'Detailed category description in Arabic', 'TEXT', NULL, 'textarea', NULL, NULL, NULL, 'أدخل وصف الفئة', 'Enter category description', 'وصف تفصيلي يساعد في فهم الفئة', 'Detailed description to help understand the category', 0, 0, 0, 6),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'description_en', 'الوصف بالإنجليزية', 'Description (English)', 'وصف تفصيلي للفئة بالإنجليزية', 'Detailed category description in English', 'TEXT', NULL, 'textarea', NULL, NULL, NULL, 'أدخل الوصف بالإنجليزية', 'Enter English description', 'وصف الفئة باللغة الإنجليزية', 'Category description in English', 0, 0, 0, 7),

-- حقول التسلسل الهرمي (تبويب التسلسل الهرمي)
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='hierarchy'), 'parent_category_id', 'الفئة الأب', 'Parent Category', 'الفئة الأب في التسلسل الهرمي', 'Parent category in hierarchy', 'INT', NULL, 'select', NULL, '{"source": "inventory_categories", "display": "category_name_ar", "value": "category_id", "where": "company_id = :company_id AND category_id != :current_id"}', NULL, 'اختر الفئة الأب', 'Select parent category', 'الفئة الأب (اتركها فارغة للفئة الرئيسية)', 'Parent category (leave empty for root category)', 0, 0, 0, 8),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='hierarchy'), 'category_level', 'مستوى الفئة', 'Category Level', 'مستوى الفئة في التسلسل الهرمي', 'Category level in hierarchy', 'INT', NULL, 'number', '{"min": 0, "max": 10}', NULL, '0', 'أدخل مستوى الفئة', 'Enter category level', 'مستوى الفئة (0 للفئة الرئيسية)', 'Category level (0 for root category)', 0, 0, 0, 9),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='hierarchy'), 'sort_order', 'ترتيب العرض', 'Sort Order', 'ترتيب عرض الفئة', 'Category display order', 'INT', NULL, 'number', '{"min": 0}', NULL, '0', 'أدخل ترتيب العرض', 'Enter sort order', 'ترتيب عرض الفئة (الأقل يظهر أولاً)', 'Category display order (lower shows first)', 0, 0, 0, 10),

-- حقول العرض والتصميم (تبويب العرض والتصميم)
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='display'), 'category_icon', 'أيقونة الفئة', 'Category Icon', 'أيقونة الفئة (Font Awesome)', 'Category icon (Font Awesome)', 'VARCHAR', '50', 'text', '{"pattern": "fas fa-[a-z-]+"}', NULL, 'fas fa-folder', 'أدخل أيقونة الفئة', 'Enter category icon', 'أيقونة Font Awesome للفئة (مثل: fas fa-laptop)', 'Font Awesome icon for category (e.g., fas fa-laptop)', 0, 0, 0, 11),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='display'), 'category_color', 'لون الفئة', 'Category Color', 'لون الفئة (Hex)', 'Category color (Hex)', 'VARCHAR', '7', 'color', '{"pattern": "^#[0-9A-Fa-f]{6}$"}', NULL, '#007bff', 'اختر لون الفئة', 'Choose category color', 'لون الفئة بصيغة Hex (مثل: #007bff)', 'Category color in Hex format (e.g., #007bff)', 0, 0, 0, 12),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='display'), 'image_url', 'صورة الفئة', 'Category Image', 'صورة الفئة', 'Category image', 'VARCHAR', '255', 'file', '{"accept": "image/*", "max_size": "2MB"}', NULL, NULL, 'اختر صورة الفئة', 'Choose category image', 'صورة تمثيلية للفئة', 'Representative image for category', 0, 0, 0, 13),

-- حقول تحسين محركات البحث (تبويب SEO)
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='seo'), 'slug', 'الرابط المختصر', 'URL Slug', 'الرابط المختصر للفئة', 'Category URL slug', 'VARCHAR', '200', 'text', '{"pattern": "^[a-z0-9-]+$", "max_length": 200}', NULL, NULL, 'أدخل الرابط المختصر', 'Enter URL slug', 'رابط مختصر للفئة (أحرف إنجليزية وأرقام وشرطات فقط)', 'Short URL for category (letters, numbers and dashes only)', 0, 0, 0, 14),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='seo'), 'meta_title', 'عنوان الصفحة', 'Meta Title', 'عنوان الصفحة لمحركات البحث', 'Page title for search engines', 'VARCHAR', '60', 'text', '{"max_length": 60}', NULL, NULL, 'أدخل عنوان الصفحة', 'Enter meta title', 'عنوان الصفحة (60 حرف كحد أقصى)', 'Page title (60 characters max)', 0, 0, 0, 15),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='seo'), 'meta_description', 'وصف الصفحة', 'Meta Description', 'وصف الصفحة لمحركات البحث', 'Page description for search engines', 'VARCHAR', '160', 'textarea', '{"max_length": 160}', NULL, NULL, 'أدخل وصف الصفحة', 'Enter meta description', 'وصف الصفحة (160 حرف كحد أقصى)', 'Page description (160 characters max)', 0, 0, 0, 16),

-- الإعدادات المتقدمة (تبويب الإعدادات)
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='settings'), 'is_featured', 'فئة مميزة', 'Featured Category', 'هل الفئة مميزة', 'Is category featured', 'TINYINT', '1', 'checkbox', NULL, NULL, '0', NULL, NULL, 'تحديد إذا كانت الفئة مميزة', 'Specify if category is featured', 0, 0, 0, 17),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='settings'), 'show_in_menu', 'إظهار في القائمة', 'Show in Menu', 'إظهار الفئة في قائمة التنقل', 'Show category in navigation menu', 'TINYINT', '1', 'checkbox', NULL, NULL, '1', NULL, NULL, 'إظهار الفئة في قائمة التنقل الرئيسية', 'Show category in main navigation menu', 0, 0, 0, 18),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='settings'), 'allow_products', 'السماح بالمنتجات', 'Allow Products', 'السماح بإضافة منتجات لهذه الفئة', 'Allow adding products to this category', 'TINYINT', '1', 'checkbox', NULL, NULL, '1', NULL, NULL, 'السماح بإضافة منتجات مباشرة لهذه الفئة', 'Allow adding products directly to this category', 0, 0, 0, 19),

-- حقول النظام
('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'is_active', 'نشط', 'Active', 'حالة نشاط الفئة', 'Category active status', 'TINYINT', '1', 'checkbox', NULL, NULL, '1', NULL, NULL, 'تحديد إذا كانت الفئة نشطة أم لا', 'Specify if category is active or not', 0, 1, 0, 20),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'created_by', 'أنشئ بواسطة', 'Created By', 'المستخدم الذي أنشأ الفئة', 'User who created the category', 'INT', NULL, 'hidden', NULL, NULL, NULL, NULL, NULL, 'معرف المستخدم الذي أنشأ الفئة', 'ID of user who created the category', 1, 1, 1, 21),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'updated_by', 'حُدث بواسطة', 'Updated By', 'المستخدم الذي حدث الفئة', 'User who updated the category', 'INT', NULL, 'hidden', NULL, NULL, NULL, NULL, NULL, 'معرف المستخدم الذي حدث الفئة', 'ID of user who updated the category', 0, 1, 1, 22),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'created_at', 'تاريخ الإنشاء', 'Created At', 'تاريخ إنشاء الفئة', 'Category creation date', 'TIMESTAMP', NULL, 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP', NULL, NULL, 'تاريخ ووقت إنشاء الفئة', 'Date and time of category creation', 0, 1, 1, 23),

('inventory', 'categories', (SELECT tab_id FROM system_tabs WHERE module_name='inventory' AND table_name='categories' AND tab_name='basic_info'), 'updated_at', 'تاريخ التحديث', 'Updated At', 'تاريخ آخر تحديث للفئة', 'Category last update date', 'TIMESTAMP', NULL, 'hidden', NULL, NULL, 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', NULL, NULL, 'تاريخ ووقت آخر تحديث للفئة', 'Date and time of last category update', 0, 1, 1, 24);

-- ========================================
-- 3. جدول الفئات الأساسي
-- ========================================
CREATE TABLE IF NOT EXISTS `inventory_categories` (
  `category_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT 'معرف الشركة',
  `module_code` varchar(50) DEFAULT 'inventory' COMMENT 'كود الوحدة',
  `created_by` int NOT NULL COMMENT 'المستخدم الذي أنشأ الفئة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`category_id`),
  FOREIGN KEY (`company_id`) REFERENCES `companies`(`CompanyID`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`UserID`) ON DELETE RESTRICT,
  KEY `idx_company` (`company_id`),
  KEY `idx_module` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الفئات الأساسي';

-- ========================================
-- 4. إعداد الحقول الافتراضية للشركة رقم 4
-- ========================================
INSERT INTO `company_field_selections` (
    `company_id`, `field_id`, `is_enabled`, `is_visible_form`, `is_visible_table`,
    `is_visible_details`, `is_required`, `is_searchable`, `is_filterable`,
    `is_sortable`, `is_exportable`, `display_order_form`, `display_order_table`,
    `configured_by`, `configured_at`
) 
SELECT 
    4 as company_id,
    field_id,
    CASE 
        WHEN field_name IN ('category_id', 'company_id', 'created_by', 'updated_by', 'created_at', 'updated_at') THEN 0
        ELSE 1 
    END as is_enabled,
    CASE 
        WHEN field_name IN ('category_id', 'company_id', 'created_by', 'updated_by', 'created_at', 'updated_at') THEN 0
        ELSE 1 
    END as is_visible_form,
    CASE 
        WHEN field_name IN ('category_code', 'category_name_ar', 'category_name_en', 'parent_category_id', 'is_active') THEN 1
        ELSE 0 
    END as is_visible_table,
    CASE 
        WHEN field_name IN ('category_id', 'company_id', 'created_by', 'updated_by') THEN 0
        ELSE 1 
    END as is_visible_details,
    CASE 
        WHEN field_name IN ('category_code', 'category_name_ar', 'company_id', 'created_by') THEN 1
        ELSE 0 
    END as is_required,
    CASE 
        WHEN field_name IN ('category_code', 'category_name_ar', 'category_name_en', 'description_ar') THEN 1
        ELSE 0 
    END as is_searchable,
    CASE 
        WHEN field_name IN ('parent_category_id', 'is_featured', 'is_active') THEN 1
        ELSE 0 
    END as is_filterable,
    1 as is_sortable,
    1 as is_exportable,
    display_order as display_order_form,
    display_order as display_order_table,
    32 as configured_by,
    NOW() as configured_at
FROM system_fields 
WHERE module_name = 'inventory' AND table_name = 'categories';

-- ========================================
-- 5. بيانات افتراضية للفئات
-- ========================================

-- إنشاء فئات افتراضية في الجدول الأساسي
INSERT INTO `inventory_categories` (`category_id`, `company_id`, `module_code`, `created_by`, `created_at`) VALUES
(1, 4, 'inventory', 32, NOW()),
(2, 4, 'inventory', 32, NOW()),
(3, 4, 'inventory', 32, NOW()),
(4, 4, 'inventory', 32, NOW()),
(5, 4, 'inventory', 32, NOW()),
(6, 4, 'inventory', 32, NOW()),
(7, 4, 'inventory', 32, NOW()),
(8, 4, 'inventory', 32, NOW());
