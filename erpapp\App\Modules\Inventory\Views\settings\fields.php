<?php
// التحقق من الجلسة
if (!isset($_SESSION['user_id'])) {
    redirect('/login');
    exit;
}

$pageTitle = 'إعدادات حقول المخزون';
include BASE_PATH . '/App/Views/layouts/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-cogs"></i> إعدادات حقول المخزون</h2>
                    <p class="text-muted">تخصيص الحقول المطلوبة لوحدة المخزون</p>
                </div>
                <div>
                    <?php if (!$hasTable): ?>
                    <button type="button" class="btn btn-success" id="createDefaultsBtn">
                        <i class="fas fa-magic"></i> إنشاء إعدادات افتراضية
                    </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-primary" id="previewBtn">
                        <i class="fas fa-eye"></i> معاينة الجدول
                    </button>
                </div>
            </div>

            <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>

            <!-- معلومات الحالة -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle text-info"></i> حالة الجدول
                            </h6>
                            <?php if ($hasTable): ?>
                            <span class="badge badge-success">
                                <i class="fas fa-check"></i> تم إنشاء الجدول
                            </span>
                            <small class="text-muted d-block mt-1">
                                الجدول: <?= htmlspecialchars($hasTable) ?>
                            </small>
                            <?php else: ?>
                            <span class="badge badge-warning">
                                <i class="fas fa-exclamation-triangle"></i> لم يتم إنشاء الجدول بعد
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-list text-primary"></i> الحقول المفعلة
                            </h6>
                            <span class="badge badge-primary">
                                <?= count($enabledFieldIds) ?> حقل مفعل
                            </span>
                            <small class="text-muted d-block mt-1">
                                من أصل <?= count($fieldsByCategory) > 0 ? array_sum(array_map('count', $fieldsByCategory)) : 0 ?> حقل متاح
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إعدادات الحقول -->
            <form id="fieldsForm" method="POST" action="/inventory/settings/fields/update">
                <input type="hidden" name="module_code" value="<?= htmlspecialchars($moduleCode) ?>">
                <input type="hidden" name="entity_type" value="<?= htmlspecialchars($entityType) ?>">

                <?php if (!empty($fieldsByCategory)): ?>
                    <?php foreach ($fieldsByCategory as $category => $fields): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-folder"></i> 
                                <?= htmlspecialchars($categoryNames[$category] ?? $category) ?>
                                <span class="badge badge-secondary ml-2"><?= count($fields) ?> حقل</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th width="5%">
                                                <input type="checkbox" class="category-toggle" data-category="<?= $category ?>">
                                            </th>
                                            <th width="25%">اسم الحقل</th>
                                            <th width="15%">نوع البيانات</th>
                                            <th width="10%">مطلوب</th>
                                            <th width="10%">مرئي</th>
                                            <th width="10%">قابل للبحث</th>
                                            <th width="10%">في القوائم</th>
                                            <th width="10%">الترتيب</th>
                                            <th width="5%">معلومات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($fields as $field): ?>
                                        <?php 
                                        $fieldId = $field['field_id'];
                                        $isEnabled = in_array($fieldId, $enabledFieldIds);
                                        $isCoreField = $field['is_core_field'] == 1;
                                        ?>
                                        <tr class="<?= $isEnabled ? 'table-success' : '' ?>">
                                            <td>
                                                <input type="checkbox" 
                                                       name="fields[<?= $fieldId ?>][enabled]" 
                                                       value="1"
                                                       <?= $isEnabled ? 'checked' : '' ?>
                                                       <?= $isCoreField ? 'disabled' : '' ?>
                                                       class="field-checkbox"
                                                       data-category="<?= $category ?>">
                                                <?php if ($isCoreField): ?>
                                                <input type="hidden" name="fields[<?= $fieldId ?>][enabled]" value="1">
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($field['field_name_ar']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= htmlspecialchars($field['field_name_en']) ?></small>
                                                <?php if ($isCoreField): ?>
                                                <span class="badge badge-danger badge-sm">أساسي</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <code><?= htmlspecialchars($field['input_type']) ?></code>
                                            </td>
                                            <td>
                                                <input type="checkbox" 
                                                       name="fields[<?= $fieldId ?>][required]" 
                                                       value="1"
                                                       <?= $isEnabled && ($field['is_required_default'] || $isCoreField) ? 'checked' : '' ?>
                                                       <?= !$isEnabled || $isCoreField ? 'disabled' : '' ?>
                                                       class="form-control-sm">
                                            </td>
                                            <td>
                                                <input type="checkbox" 
                                                       name="fields[<?= $fieldId ?>][visible]" 
                                                       value="1"
                                                       <?= $isEnabled && $field['input_type'] != 'hidden' ? 'checked' : '' ?>
                                                       <?= !$isEnabled || $field['input_type'] == 'hidden' ? 'disabled' : '' ?>
                                                       class="form-control-sm">
                                            </td>
                                            <td>
                                                <input type="checkbox" 
                                                       name="fields[<?= $fieldId ?>][searchable]" 
                                                       value="1"
                                                       <?= $isEnabled && in_array($field['input_type'], ['text', 'select']) ? 'checked' : '' ?>
                                                       <?= !$isEnabled || !in_array($field['input_type'], ['text', 'select', 'textarea']) ? 'disabled' : '' ?>
                                                       class="form-control-sm">
                                            </td>
                                            <td>
                                                <input type="checkbox" 
                                                       name="fields[<?= $fieldId ?>][listable]" 
                                                       value="1"
                                                       <?= $isEnabled && $field['input_type'] != 'hidden' ? 'checked' : '' ?>
                                                       <?= !$isEnabled || $field['input_type'] == 'hidden' ? 'disabled' : '' ?>
                                                       class="form-control-sm">
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       name="fields[<?= $fieldId ?>][order]" 
                                                       value="<?= $field['display_order'] ?>"
                                                       min="0" max="999"
                                                       class="form-control form-control-sm"
                                                       style="width: 70px;"
                                                       <?= !$isEnabled ? 'disabled' : '' ?>>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-info" 
                                                        data-toggle="tooltip" 
                                                        title="<?= htmlspecialchars($field['description_ar']) ?>">
                                                    <i class="fas fa-info"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <!-- أزرار الحفظ -->
                    <div class="card">
                        <div class="card-body text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                            <?php if (!$hasTable && count($enabledFieldIds) > 0): ?>
                            <button type="button" class="btn btn-success btn-lg ml-2" id="createTableBtn">
                                <i class="fas fa-table"></i> إنشاء الجدول
                            </button>
                            <?php endif; ?>
                            <a href="/inventory" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h5>لا توجد حقول متاحة</h5>
                        <p>لم يتم العثور على حقول متاحة لهذه الوحدة.</p>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</div>

<!-- Modal معاينة الجدول -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة هيكل الجدول</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // تبديل تحديد الفئة
    $('.category-toggle').change(function() {
        const category = $(this).data('category');
        const isChecked = $(this).is(':checked');
        
        $(`.field-checkbox[data-category="${category}"]`).each(function() {
            if (!$(this).is(':disabled')) {
                $(this).prop('checked', isChecked);
                toggleFieldOptions(this);
            }
        });
    });
    
    // تبديل خيارات الحقل عند التفعيل/الإلغاء
    $('.field-checkbox').change(function() {
        toggleFieldOptions(this);
    });
    
    function toggleFieldOptions(checkbox) {
        const row = $(checkbox).closest('tr');
        const isEnabled = $(checkbox).is(':checked');
        
        row.find('input[type="checkbox"], input[type="number"]').not(checkbox).prop('disabled', !isEnabled);
        
        if (isEnabled) {
            row.addClass('table-success');
        } else {
            row.removeClass('table-success');
        }
    }
    
    // حفظ الإعدادات
    $('#fieldsForm').submit(function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.ajax({
            url: '/inventory/settings/fields/update',
            method: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ في الاتصال');
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> حفظ الإعدادات');
            }
        });
    });
    
    // إنشاء الجدول
    $('#createTableBtn').click(function() {
        if (!confirm('هل أنت متأكد من إنشاء الجدول؟ سيتم إنشاء جدول جديد بالحقول المفعلة.')) {
            return;
        }
        
        $.ajax({
            url: '/inventory/settings/fields/create-table',
            method: 'POST',
            data: {
                module_code: '<?= $moduleCode ?>',
                entity_type: '<?= $entityType ?>'
            },
            dataType: 'json',
            beforeSend: function() {
                $('#createTableBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...');
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ في إنشاء الجدول');
            },
            complete: function() {
                $('#createTableBtn').prop('disabled', false).html('<i class="fas fa-table"></i> إنشاء الجدول');
            }
        });
    });
    
    // إنشاء إعدادات افتراضية
    $('#createDefaultsBtn').click(function() {
        $.ajax({
            url: '/inventory/settings/fields/create-defaults',
            method: 'POST',
            data: {
                module_code: '<?= $moduleCode ?>',
                entity_type: '<?= $entityType ?>'
            },
            dataType: 'json',
            beforeSend: function() {
                $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...');
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ في إنشاء الإعدادات');
            },
            complete: function() {
                $('#createDefaultsBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> إنشاء إعدادات افتراضية');
            }
        });
    });
    
    // معاينة الجدول
    $('#previewBtn').click(function() {
        $.ajax({
            url: '/inventory/settings/fields/preview',
            method: 'GET',
            data: {
                module: '<?= $moduleCode ?>',
                entity: '<?= $entityType ?>'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    let html = '<h6>اسم الجدول: <code>' + response.table_name + '</code></h6>';
                    html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
                    html += '<thead><tr><th>اسم الحقل</th><th>النوع</th><th>مطلوب</th><th>مرئي</th></tr></thead><tbody>';
                    
                    response.fields.forEach(function(field) {
                        html += '<tr>';
                        html += '<td>' + field.field_name_ar + '</td>';
                        html += '<td><code>' + field.mysql_type + '</code></td>';
                        html += '<td>' + (field.is_required ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-muted"></i>') + '</td>';
                        html += '<td>' + (field.is_visible ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-muted"></i>') + '</td>';
                        html += '</tr>';
                    });
                    
                    html += '</tbody></table></div>';
                    $('#previewContent').html(html);
                    $('#previewModal').modal('show');
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ في معاينة الجدول');
            }
        });
    });
    
    function showAlert(type, message) {
        const alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>`;
        
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>

<?php include BASE_PATH . '/App/Views/layouts/footer.php'; ?>
