-- ========================================
-- إصلاح سريع لقاعدة البيانات
-- ========================================

-- حذف البيانات التجريبية الموجودة للشركة رقم 4
DELETE FROM `dynamic_field_values` WHERE `company_id` = 4 AND `module_name` = 'inventory' AND `table_name` = 'products';
DELETE FROM `inventory_products` WHERE `company_id` = 4 AND `module_code` = 'inventory';

-- إعادة تعيين AUTO_INCREMENT
ALTER TABLE `inventory_products` AUTO_INCREMENT = 1;

-- إنشاء منتج تجريبي واحد للاختبار
INSERT INTO `inventory_products` (
    `company_id`, `module_code`, `product_code`, `product_name_ar`, 
    `category_id`, `unit_id`, `cost_price`, `selling_price`, `is_active`, 
    `created_by`, `created_at`
) VALUES
(4, 'inventory', 'TEST001', 'منتج تجريبي للاختبار', 1, 1, 100.00, 150.00, 1, 32, NOW());

-- إدراج بعض القيم الديناميكية للمنتج التجريبي
INSERT INTO `dynamic_field_values` (`company_id`, `module_name`, `table_name`, `record_id`, `field_id`, `field_value`, `created_by`, `created_at`) VALUES
-- المنتج التجريبي (معرف المنتج = 1)
(4, 'inventory', 'products', 1, 5, 'Test Product', 32, NOW()), -- product_name_en
(4, 'inventory', 'products', 1, 6, '*********', 32, NOW()), -- barcode
(4, 'inventory', 'products', 1, 7, 'منتج تجريبي لاختبار النظام الديناميكي', 32, NOW()), -- description_ar
(4, 'inventory', 'products', 1, 8, 'Test product for dynamic system', 32, NOW()), -- description_en
(4, 'inventory', 'products', 1, 11, 'منتج', 32, NOW()), -- product_type
(4, 'inventory', 'products', 1, 15, '1', 32, NOW()), -- track_inventory
(4, 'inventory', 'products', 1, 16, '10', 32, NOW()), -- current_stock
(4, 'inventory', 'products', 1, 17, '5', 32, NOW()), -- min_stock_level
(4, 'inventory', 'products', 1, 41, '15.00', 32, NOW()); -- tax_rate

-- التحقق من النتائج
SELECT 'تم إنشاء المنتج التجريبي بنجاح' as message;
SELECT COUNT(*) as total_products FROM inventory_products WHERE company_id = 4;
SELECT COUNT(*) as total_dynamic_values FROM dynamic_field_values WHERE company_id = 4 AND module_name = 'inventory';

-- ========================================
-- تعليمات الاستخدام
-- ========================================

/*
1. شغل هذا الملف في قاعدة البيانات
2. اذهب إلى: /erpapp/inventory/products/create
3. جرب إنشاء منتج جديد
4. يجب أن يعمل بدون أخطاء

إذا ظهرت أخطاء:
- تأكد من أن current_company_id = 4 للمستخدم
- تأكد من وجود فئات ووحدات قياس بمعرفات 1 و 2
- شغل dynamic_system_tables.sql الكامل
*/
