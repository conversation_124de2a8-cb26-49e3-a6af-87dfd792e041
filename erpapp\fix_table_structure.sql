-- ========================================
-- إصلاح بنية جدول inventory_products
-- ========================================

-- أولاً: فحص البنية الحالية
DESCRIBE inventory_products;

-- إذا كان الجدول موجود، نحذفه ونعيد إنشاؤه
DROP TABLE IF EXISTS inventory_products;

-- إنشاء جدول inventory_products بالبنية الصحيحة
CREATE TABLE `inventory_products` (
  `product_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT 'معرف الشركة',
  `module_code` varchar(50) NOT NULL DEFAULT 'inventory' COMMENT 'كود الوحدة',
  `product_code` varchar(100) NOT NULL COMMENT 'كود المنتج',
  `product_name_ar` varchar(255) NOT NULL COMMENT 'اسم المنتج بالعربية',
  `product_name_en` varchar(255) DEFAULT NULL COMMENT 'اسم المنتج بالإنجليزية',
  `category_id` int DEFAULT 1 COMMENT 'معرف الفئة',
  `unit_id` int DEFAULT 1 COMMENT 'معرف وحدة القياس',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر البيع',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة التفعيل',
  `created_by` int NOT NULL COMMENT 'المستخدم الذي أنشأ السجل',
  `updated_by` int DEFAULT NULL COMMENT 'المستخدم الذي حدث السجل',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `unique_company_product_code` (`company_id`, `product_code`),
  KEY `idx_company_module` (`company_id`, `module_code`),
  KEY `idx_category` (`category_id`),
  KEY `idx_unit` (`unit_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول المنتجات الأساسي';

-- إنشاء فئات افتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `product_categories` (`category_id`, `category_name_ar`, `category_name_en`, `is_active`, `created_at`) VALUES
(1, 'إلكترونيات', 'Electronics', 1, NOW()),
(2, 'كتب', 'Books', 1, NOW()),
(3, 'ملابس', 'Clothing', 1, NOW());

-- إنشاء وحدات قياس افتراضية إذا لم تكن موجودة
INSERT IGNORE INTO `product_units` (`unit_id`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_active`, `created_at`) VALUES
(1, 'قطعة', 'Piece', 'قطعة', 'pcs', 1, NOW()),
(2, 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, NOW()),
(3, 'متر', 'Meter', 'م', 'm', 1, NOW());

-- الآن إدراج البيانات التجريبية
INSERT INTO `inventory_products` (
    `company_id`, `module_code`, `product_code`, `product_name_ar`, 
    `category_id`, `unit_id`, `cost_price`, `selling_price`, `is_active`, 
    `created_by`, `created_at`
) VALUES
(4, 'inventory', 'LAP001', 'لابتوب ديل انسبايرون 15', 1, 1, 2500.00, 3200.00, 1, 32, NOW()),
(4, 'inventory', 'PHN002', 'هاتف سامسونج جالاكسي A54', 1, 1, 1200.00, 1650.00, 1, 32, NOW()),
(4, 'inventory', 'BOK003', 'كتاب تعلم البرمجة', 2, 1, 45.00, 75.00, 1, 32, NOW());

-- إدراج بعض القيم الديناميكية للمنتجات
INSERT INTO `dynamic_field_values` (`company_id`, `module_name`, `table_name`, `record_id`, `field_id`, `field_value`, `created_by`, `created_at`) VALUES
-- المنتج الأول: لابتوب ديل (معرف المنتج = 1)
(4, 'inventory', 'products', 1, 5, 'Dell Inspiron 15 Laptop', 32, NOW()), -- product_name_en
(4, 'inventory', 'products', 1, 6, '123456789012', 32, NOW()), -- barcode
(4, 'inventory', 'products', 1, 7, 'لابتوب عالي الأداء مناسب للعمل والدراسة', 32, NOW()), -- description_ar
(4, 'inventory', 'products', 1, 15, '1', 32, NOW()), -- track_inventory
(4, 'inventory', 'products', 1, 16, '15', 32, NOW()), -- current_stock
(4, 'inventory', 'products', 1, 17, '5', 32, NOW()), -- min_stock_level

-- المنتج الثاني: هاتف سامسونج (معرف المنتج = 2)
(4, 'inventory', 'products', 2, 5, 'Samsung Galaxy A54 Phone', 32, NOW()), -- product_name_en
(4, 'inventory', 'products', 2, 6, '987654321098', 32, NOW()), -- barcode
(4, 'inventory', 'products', 2, 7, 'هاتف ذكي بكاميرا عالية الجودة', 32, NOW()), -- description_ar
(4, 'inventory', 'products', 2, 15, '1', 32, NOW()), -- track_inventory
(4, 'inventory', 'products', 2, 16, '25', 32, NOW()), -- current_stock
(4, 'inventory', 'products', 2, 17, '10', 32, NOW()), -- min_stock_level

-- المنتج الثالث: كتاب (معرف المنتج = 3)
(4, 'inventory', 'products', 3, 5, 'Programming Learning Book', 32, NOW()), -- product_name_en
(4, 'inventory', 'products', 3, 6, '456789123456', 32, NOW()), -- barcode
(4, 'inventory', 'products', 3, 7, 'كتاب شامل لتعلم البرمجة للمبتدئين', 32, NOW()), -- description_ar
(4, 'inventory', 'products', 3, 15, '1', 32, NOW()), -- track_inventory
(4, 'inventory', 'products', 3, 16, '50', 32, NOW()), -- current_stock
(4, 'inventory', 'products', 3, 17, '20', 32, NOW()); -- min_stock_level

-- التحقق من النتائج
SELECT 'تم إنشاء الجدول والبيانات بنجاح' as message;
SELECT COUNT(*) as total_products FROM inventory_products WHERE company_id = 4;
SELECT COUNT(*) as total_dynamic_values FROM dynamic_field_values WHERE company_id = 4 AND module_name = 'inventory';

-- عرض بنية الجدول الجديدة
DESCRIBE inventory_products;
